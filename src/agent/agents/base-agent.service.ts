import {
  AIMessage,
  BaseMessage,
  HumanMessage,
  SystemMessage,
} from '@langchain/core/messages';
import { Annotation, END, MemorySaver } from '@langchain/langgraph';
import {
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { ToolNode } from '@langchain/langgraph/prebuilt';
import { GraphState, ModelWithTools, OpenAIModel } from 'src/agent/types';
import { formatDate } from 'src/utils/format-date.util';
import { compareDates } from 'src/utils/compare-dates.util';

@Injectable()
export abstract class BaseAgentService {
  protected agents = new Map<string, { agent: any }>();
  protected logger: Logger;
  protected graphState: GraphState;
  protected tools: any[];
  protected toolNode: ToolNode;
  protected model: OpenAIModel;
  protected modelWithTools: ModelWithTools;
  protected workflow: any;  
  protected prompt: string;
  protected checkPointer: MemorySaver;

  abstract createAgent(
    name: string,
    systemPrompt: string,
    model: { name: string; temperature: number; cache: boolean },
    tools: any[],
  ): Promise<{ agent: any }>;

  protected abstract initializeWorkflow(): any;

  protected initializeGraphState(): GraphState {
    return Annotation.Root({
      messages: Annotation<BaseMessage[]>({
        reducer: (a: BaseMessage[], b: BaseMessage[]) => a.concat(b),
        default: () => [],
      }),
      chat_history: Annotation<BaseMessage[]>({
        reducer: (a, b) => a.concat(b),
        default: () => [],
      }),
    });
  }

  private getAgent(agentId: string) {
    if (!this.agents.has(agentId)) throw new Error('Agent not initialized.');
    return this.agents.get(agentId);
  }

  protected shouldContinue(state: typeof this.graphState.State) {
    const messages = state.messages;
    const lastMessage = messages[messages.length - 1];

    if (!lastMessage || !('tool_calls' in lastMessage)) {
      this.logger.debug('---NO TOOL CALLS FOUND. SKIPPING RETRIEVE---');
      return END;
    }

    if (
      Array.isArray(lastMessage.tool_calls) &&
      lastMessage.tool_calls.length === 0
    ) {
      this.logger.debug('---NO TOOLS REQUESTED BY AI. SKIPPING RETRIEVE---');
      return END;
    }

    this.logger.debug('---TOOL CALLING---');
    this.logger.debug(`---TOOL: ${lastMessage.tool_calls?.[0]?.name}---`);
    return 'tools';
  }

  protected async callModel(state: typeof this.graphState.State) {
    this.logger.debug('---CALL MODEL---');
    const { messages, chat_history } = state;
    const systemMessage = new SystemMessage(this.prompt);

    const response = await this.modelWithTools.invoke([
      systemMessage,
      ...chat_history,
      ...messages,
    ]);

    return { messages: [response] };
  }

  async generateAnswer(
    agentName: string,
    input: string,
    chatHistory: (HumanMessage | AIMessage)[],
  ) {
    const agent = this.getAgent(agentName);

    if (!agent.agent) {
      throw new NotFoundException('Agent not found');
    }

    const startDateTime = new Date();
    const formattedStartDateTime = formatDate(startDateTime);
    this.logger.debug(`---GENERATE ANSWER---`);
    this.logger.debug(`---START AT: ${formattedStartDateTime}---`);
    this.logger.debug(`---USER MESSAGE---`);
    this.logger.debug(`---MESSAGE: ${input}---`);

    try {
      const messages = chatHistory;

      const finalState = await agent.agent.invoke({
        messages,
        chat_history: messages,
      });

      const agentAnswer =
        finalState.messages[finalState.messages.length - 1].content;

      const endDateTime = new Date();
      const formattedEndDateTime = formatDate(endDateTime);
      this.logger.debug(`---END AT: ${formattedEndDateTime}---`);

      const timeDiff = compareDates(startDateTime, endDateTime);
      this.logger.debug(
        `---PROCESS DURATION: ${timeDiff.seconds}.${timeDiff.milliseconds}---`,
      );

      return agentAnswer;
    } catch (error) {
      this.logger.error('Error generating answer:', error);

      if (error instanceof NotFoundException) throw error;

      throw new InternalServerErrorException('Failed to generate answer.');
    }
  }
}
