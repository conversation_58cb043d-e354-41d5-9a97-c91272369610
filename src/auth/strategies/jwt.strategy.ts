import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { UserRoles } from '@prisma/client';
import { Request } from 'express';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private readonly configService: ConfigService) {
    super({
      jwtFromRequest: ExtractJwt.fromExtractors([
        (request: Request) => {
          return (
            request?.headers?.authorization?.split(' ')?.[1] ||
            request?.cookies?.authToken ||
            null
          );
        },
      ]),
      algorithms: ['HS256'],
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('auth.jwt_secret'),
    });
  }

  async validate(payload: {
    sub: string;
    role: UserRoles;
    sessionid: string;
    exp: Date;
  }) {
    return {
      role: payload.role,
      userid: payload.sub,
      sessionid: payload.sessionid,
      exp: payload.exp,
    };
  }
}
