import {
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { User } from '@prisma/client';
import * as bcrypt from 'bcryptjs';
import { UserRequestPayload } from 'src/types';
import { UserService } from 'src/user/user.service';

@Injectable()
export class AuthService {
  constructor(
    @Inject(forwardRef(() => UserService))
    private readonly userService: UserService,
    private jwtService: JwtService,
  ) {}

  async validateUser(
    userName: string,
    password: string,
  ): Promise<Pick<User, 'id' | 'email' | 'role' | 'userName'>> {
    try {
      const user = await this.userService.getUser({ userName });

      if (!user) {
        return null;
      }

      const isPasswordValid = await bcrypt.compare(password, user.password);

      if (!isPasswordValid) {
        return null;
      }

      return {
        id: user.id,
        email: user.email,
        role: user.role,
        userName: user.userName,
      };
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException('User details not found');
      }
    }
  }

  async login(user: UserRequestPayload) {
    const payload = {
      sub: user.id,
      role: user.role,
      userid: user.id,
      sessionid: `session-${user.id}`,
    };
    return {
      user_id: user.id,
      access_token: this.jwtService.sign(payload),
    };
  }

  async me(id: string): Promise<Omit<User, 'password'>> {
    const user = await this.userService.getUser(
      { id },
      {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        userName: true,
        phoneNumber: true,
        role: true,
      },
    );

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }
}
