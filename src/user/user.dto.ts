import { UserRoles } from '@prisma/client';
import {
  IsEmail,
  IsEnum,
  IsPhoneNumber,
  IsString,
  IsStrongPassword,
} from 'class-validator';

export class UserDto {
  @IsString()
  firstName: string;

  @IsString()
  lastName: string;

  @IsEmail()
  email: string;

  @IsPhoneNumber('US')
  phoneNumber: string;

  @IsString()
  userName: string;

  @IsStrongPassword({
    minLength: 8,
    minSymbols: 2,
    minNumbers: 2,
    minLowercase: 2,
  })
  password: string;

  @IsEnum(UserRoles)
  role: UserRoles;
}
