import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { AgentConfig, ChatMessageRole, LLMModel } from '@prisma/client';
import { AgentManageService } from './agent.manage.service';
import { RetrieveTool } from 'src/tools/retrieve-tool/retrieve-tool';
import { ChatMessageService } from 'src/chat-message/chat-message.service';
import { ReactAgentService } from './agents/react-agent/react-agent.service';
import { HumanMessage, AIMessage } from '@langchain/core/messages';

@Injectable()
export class AgentService {
  constructor(
    private readonly retrieveTool: RetrieveTool,
    private readonly agentManageService: AgentManageService,
    private readonly chatMessageService: ChatMessageService,
    private readonly reactAgentService: ReactAgentService,
  ) {}

  async generateAnswer(
    userId: string,
    sessionId: string,
    input: string,
    agentName?: string,
  ) {
    try {
      const chatHistory = await this.chatMessageService.getChatHistory(
        userId,
        '',
      );
      const messages = chatHistory.map((message) =>
        message.role === ChatMessageRole.user
          ? new HumanMessage(message.content)
          : new AIMessage(message.content),
      );

      messages.push(new HumanMessage(input));

      let agentConfigs: AgentConfig & { model: LLMModel };

      if (agentName) {
        agentConfigs = await this.agentManageService.getAgentByName(agentName);
      } else {
        agentConfigs = await this.agentManageService.getAgent({
          default: true,
        });
      }

      const agent = await this.reactAgentService.createAgent(
        agentConfigs.name,
        agentConfigs.systemPrompt,
        {
          name: agentConfigs.model.name,
          temperature: agentConfigs.temperature,
          cache: agentConfigs.cacheEnabled,
        },
        [this.retrieveTool.getTool()],
      );

      if (!agent.agent) {
        throw new InternalServerErrorException('Agent Creation Failed');
      }

      const answer = await this.reactAgentService.generateAnswer(
        agentConfigs.name,
        input,
        messages,
      );

      await this.chatMessageService.saveMessage(
        userId,
        sessionId,
        ChatMessageRole.user,
        input,
      );
      await this.chatMessageService.saveMessage(
        userId,
        sessionId,
        ChatMessageRole.assistant,
        answer,
      );

      return answer;
    } catch (error) {
      throw error;
    }
  }
}
