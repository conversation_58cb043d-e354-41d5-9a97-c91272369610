import {
  Inject,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import {
  IAyrShareConfig,
  ISocialMediaPost,
  Platform,
} from './ayrshare.config.interface';

@Injectable()
export class AyrsharePostService {
  constructor(
    @Inject('AYRSHARE_MODULE_OPTIONS') private options: IAyrShareConfig,
  ) {}

  /**
   * @name getPosts
   * @description This method returns the ayrshare posts for the provided social media platform
   * @param platform facebook, instagram
   * @returns 
   * ```typescript
   * {
      id: string;
      createdAt: Date;
      post?: string;
      postUrl?: string;
      commentsCount: number;
      platformTag: platform;
    }[]
   */
  async getPosts(platform: Platform): Promise<ISocialMediaPost[]> {
    switch (platform) {
      case Platform.FACEBOOK:
        return await this.getFacebookPosts();
      case Platform.INSTAGRAM:
        return await this.getInstagramPosts();
      default:
        return [];
    }
  }

  private async getFacebookPosts(): Promise<ISocialMediaPost[]> {
    const facebookPostsResponse = await this.fetchPlatformPosts(
      Platform.FACEBOOK,
    );
    const facebookPosts: any[] = facebookPostsResponse.posts;
    const formattedFacebookPosts: ISocialMediaPost[] = [];

    if (!facebookPosts.length) {
      return formattedFacebookPosts;
    }

    for (const fbPost of facebookPosts) {
      formattedFacebookPosts.push({
        postId: fbPost.id,
        createdAt: new Date(fbPost.created),
        commentsCount: fbPost.commentsCount,
        post: fbPost.post,
        postUrl: fbPost.postUrl,
        platformTag: Platform.FACEBOOK,
      });
    }

    return formattedFacebookPosts;
  }

  private async getInstagramPosts(): Promise<ISocialMediaPost[]> {
    const instagramPostsResponse = await this.fetchPlatformPosts(
      Platform.INSTAGRAM,
    );
    const instagramPosts: any[] = instagramPostsResponse.posts;
    const formattedInstagramPosts: ISocialMediaPost[] = [];

    if (!instagramPosts.length) {
      return formattedInstagramPosts;
    }

    for (const instagramPost of instagramPosts) {
      formattedInstagramPosts.push({
        postId: instagramPost.id,
        createdAt: new Date(instagramPost.created),
        commentsCount: instagramPost.commentsCount,
        post: instagramPost.post,
        postUrl: instagramPost.postUrl,
        platformTag: Platform.INSTAGRAM,
      });
    }

    return formattedInstagramPosts;
  }

  private async fetchPlatformPosts(platform: Platform) {
    const response = await fetch(
      `${this.options.baseUrl}/history/${platform}`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${this.options.apiKey}`,
        },
      },
    );

    if (!response.ok) {
      throw new InternalServerErrorException(
        `Exception with fetching ${platform} posts`,
      );
    }

    return await response.json();
  }
}
