import {
  Inject,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import {
  IAyrShareConfig,
  ISocialMediaPost,
  Platform,
} from './ayrshare.config.interface';
import { AyrsharePostService } from './ayrshare.post.service';

@Injectable()
export class AyrshareService {
  constructor(
    @Inject('AYRSHARE_MODULE_OPTIONS') private options: IAyrShareConfig,
    private readonly ayrsharePostService: AyrsharePostService,
  ) {}

  /**
   * @name getPosts
   * @description This method returns the ayrshare posts for the provided social media platform
   * @param platform facebook, instagram
   * @returns 
   * ```typescript
   * {
      id: string;
      createdAt: Date;
      post?: string;
      postUrl?: string;
      commentsCount: number;
      platformTag: platform;
    }[]
   */
  async getPosts(platform: Platform): Promise<ISocialMediaPost[]> {
    return await this.ayrsharePostService.getPosts(platform);
  }
}
