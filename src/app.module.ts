import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UserModule } from './user/user.module';
import { AuthModule } from './auth/auth.module';
import { DocumentProcessorModule } from './document-processor/document-processor.module';
import { VectorStoreService } from './vector-store/vector-store.service';
import { ToolsModule } from './tools/tools.module';
import { AgentModule } from './agent/agent.module';
import { ChatMessageModule } from './chat-message/chat-message.module';
import { SocialMediaModule } from './social-media/social-media.module';
import configuration from './config/configuration';
import { ScheduleModule } from '@nestjs/schedule';
import { ModelModule } from './model/model.module';
import { RagAgentModule } from './rag-agent/rag-agent.module';
import { KnowledgeBaseModule } from './knowledge-base/knowledge-base.module';
import { SupportModule } from './support/support.module';
import { AbortModule } from './abort/abort.module';
import { SecurityModule } from './security/security.module';
import { GhlServiceModule } from './ghl/ghl.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
    }),
    ScheduleModule.forRoot(),
    UserModule,
    AuthModule,
    DocumentProcessorModule,
    AgentModule,
    ChatMessageModule,
    SocialMediaModule,
    ModelModule,
    RagAgentModule,
    KnowledgeBaseModule,
    SupportModule,
    AbortModule,
    SecurityModule,
    GhlServiceModule,
  ],
  controllers: [AppController],
  providers: [AppService, VectorStoreService],
})
export class AppModule {}
