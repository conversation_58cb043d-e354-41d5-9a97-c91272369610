import { Controller, Post, UseGuards, Get, Req, Res } from '@nestjs/common';
import { Response, Request } from 'express';
import { LocalAuthGuard } from './guards/local.guard';
import { JwtAuthGuard } from './guards/jwt.guard';
import { AuthService } from './auth.service';
import { CentralizeAIRequest, UserRequestPayload } from 'src/types';

@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @UseGuards(LocalAuthGuard)
  @Post('/login')
  async login(
    @Req() request: Request & { user: UserRequestPayload },
    @Res() response: Response,
  ) {
    try {
      const authData = await this.authService.login(request.user);

      return response.status(200).json({
        message: 'Login success',
        accessToken: authData.access_token,
        dateTime: new Date(),
      });
    } catch (error) {
      throw error;
    }
  }

  @UseGuards(JwtAuthGuard)
  @Get('/me')
  async me(@Req() req: CentralizeAIRequest) {
    return this.authService.me(req.user.userid);
  }

  @UseGuards(JwtAuthGuard)
  @Get('/check')
  async getProfile(@Req() req: CentralizeAIRequest) {
    return req.user;
  }
}
