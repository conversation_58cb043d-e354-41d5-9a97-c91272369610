{"name": "cpp-chatbot", "version": "0.0.1", "description": "", "author": "", "private": true, "type": "commonjs", "license": "UNLICENSED", "scripts": {"build": "nest build && copyfiles -u 2 src/support/templates/*.hbs dist", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\" \"libs/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "prisma:generate": "npx prisma generate", "prisma:push": "npx prisma db push && pnpm prisma:generate", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@gnosticdev/highlevel-sdk": "^3.0.1", "@langchain/anthropic": "^0.3.20", "@langchain/community": "^0.3.34", "@langchain/core": "^0.3.42", "@langchain/google-genai": "^0.2.9", "@langchain/langgraph": "^0.2.52", "@langchain/mongodb": "^0.1.0", "@langchain/openai": "^0.4.4", "@langchain/textsplitters": "^0.1.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^4.0.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "^8.1.1", "@prisma/client": "^6.4.1", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cookie-parser": "^1.4.7", "crypto": "^1.0.1", "dotenv": "^16.4.7", "express": "^4.21.2", "googleapis": "^150.0.1", "handlebars": "^4.7.8", "langchain": "^0.3.19", "mongodb": "^6.14.2", "nodemailer": "^7.0.3", "openai": "^4.85.4", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pdf-parse": "^1.1.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.8", "@types/express": "^5.0.0", "@types/jest": "^29.5.2", "@types/multer": "^1.4.12", "@types/node": "^20.3.1", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/pdf-parse": "^1.1.4", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "copyfiles": "^2.4.1", "eslint": "^9.21.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "prisma": "^6.4.1", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/src/", "<rootDir>/libs/"], "moduleNameMapper": {"^@app/llm(|/.*)$": "<rootDir>/libs/llm/src/$1", "^@openai/openai(|/.*)$": "<rootDir>/libs/openai/src/$1", "^@app/fingerprint-js(|/.*)$": "<rootDir>/libs/fingerprint-js/src/$1", "^@app/ayrshare(|/.*)$": "<rootDir>/libs/ayrshare/src/$1", "^@app/ghl(|/.*)$": "<rootDir>/libs/ghl/src/$1"}}}