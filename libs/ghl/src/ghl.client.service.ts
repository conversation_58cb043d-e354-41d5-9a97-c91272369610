import { Inject, Injectable } from '@nestjs/common';
import { IGhlConfig } from './ghl.config.interface';

type HighLevelIntegrationClient = any;

@Injectable()
export class GhlClientService {
  private client: HighLevelIntegrationClient;

  constructor(@Inject('GHL_MODULE_OPTIONS') private options: IGhlConfig) {}

  async getClient() {
    if (!this.client) {
      const { createHighLevelClient } = await import(
        '@gnosticdev/highlevel-sdk'
      );

      this.client = createHighLevelClient({}, 'integration', {
        privateToken: this.options.privateToken,
        accessType: this.options.accessType,
        scopes: this.options.scopes as any,
      });
    }

    return this.client;
  }
}
