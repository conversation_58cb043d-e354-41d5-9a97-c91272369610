import { Module } from '@nestjs/common';
import { DocumentProcessorService } from './document-processor.service';
import { DocumentProcessorController } from './document-processor.controller';
import { VectorStoreService } from 'src/vector-store/vector-store.service';

@Module({
  providers: [DocumentProcessorService, VectorStoreService],
  controllers: [DocumentProcessorController],
})
export class DocumentProcessorModule {}
