# ---------- BASE BUILDER ----------
FROM node:18.20.8-alpine AS builder

# Add dependencies needed for Prisma
RUN apk add --no-cache openssl

WORKDIR /app

# Install pnpm
RUN corepack enable && corepack prepare pnpm@latest --activate

# Copy project files
COPY package.json pnpm-lock.yaml ./
COPY prisma ./prisma/
COPY tsconfig*.json ./
COPY src ./src

# Install deps & build
RUN pnpm install --frozen-lockfile
RUN pnpm prisma generate
RUN pnpm build


# ---------- PRODUCTION IMAGE ----------
FROM node:18.20.8-alpine AS runner

WORKDIR /app

# Install only production deps
RUN corepack enable && corepack prepare pnpm@latest --activate
COPY package.json pnpm-lock.yaml ./
RUN pnpm install --prod --frozen-lockfile

# Copy only what's needed
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/prisma ./prisma
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./

# Prisma Client expects schema file
ENV PRISMA_GENERATE_SKIP_AUTOINSTALL=true

# Use a non-root user for security (optional)
RUN addgroup -S appgroup && adduser -S appuser -G appgroup
USER appuser

# Command to run app
CMD ["node", "dist/main"]
