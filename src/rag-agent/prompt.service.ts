import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';

@Injectable()
export class PromptService {
  constructor(private readonly prisma: PrismaService) {}

  async getSystemPromptByAgentId(agentId: string): Promise<string> {
    const agent = await this.prisma.agentConfig.findUnique({
      where: { id: agentId },
    });

    if (!agent) {
      throw new Error('Agent not found');
    }

    return agent.systemPrompt;
  }
}
