import { Modu<PERSON> } from '@nestjs/common';
import { AgentController } from './agent.controller';
import { VectorStoreService } from 'src/vector-store/vector-store.service';
import { ToolsModule } from 'src/tools/tools.module';
import { ToolAgentService } from './agents/tool-agent/tool-agent.service';
import { ChatMessageModule } from 'src/chat-message/chat-message.module';
import { ReactAgentService } from './agents/react-agent/react-agent.service';
import { AgentManageService } from './agent.manage.service';
import { ModelModule } from 'src/model/model.module';
import { PrismaService } from 'src/prisma.service';
import { AgentService } from './agent.service';
import { RagAgentModule } from 'src/rag-agent/rag-agent.module';
import { AbortModule } from 'src/abort/abort.module';

@Module({
  imports: [
    ToolsModule,
    ChatMessageModule,
    ModelModule,
    RagAgentModule,
    AbortModule,
  ],
  providers: [
    VectorStoreService,
    ToolAgentService,
    ReactAgentService,
    AgentManageService,
    AgentService,
    PrismaService,
  ],
  controllers: [AgentController],
})
export class AgentModule {}
