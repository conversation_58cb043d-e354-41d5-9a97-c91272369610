import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ModelService } from './model.service';
import { ModelDto } from './model.dto';
import { UserRoles } from '@prisma/client';
import { JwtAuthGuard } from 'src/auth/guards/jwt.guard';
import { RolesGuard } from 'src/auth/guards/role.guard';
import { Roles } from 'src/auth/roles/roles.decorator';

@Controller('model')
export class ModelController {
  constructor(private modelService: ModelService) {}

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRoles.ADMIN, UserRoles.SUPER_ADMIN, UserRoles.MODERATOR)
  @Post()
  async createModel(@Body() data: ModelDto) {
    return this.modelService.createModel(data);
  }

  @Get()
  async getModels(@Query('skip') skip: number, @Query('take') take: number) {
    return await this.modelService.getModels({ skip, take });
  }

  @Get(':id')
  async getModel(@Param() params: { id: string }) {
    try {
      return this.modelService.getModel(params.id);
    } catch (error) {
      return error;
    }
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRoles.ADMIN, UserRoles.SUPER_ADMIN, UserRoles.MODERATOR)
  @Put(':id')
  async updateModel(@Param() params: { id: string }, @Body() data: ModelDto) {
    return this.modelService.updateModel(params.id, data);
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRoles.ADMIN, UserRoles.SUPER_ADMIN, UserRoles.MODERATOR)
  @Put('key/:id')
  async setModelKey(
    @Param() params: { id: string },
    @Body() data: { key: string },
  ) {
    return this.modelService.setModelKey(params.id, data.key);
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRoles.ADMIN, UserRoles.SUPER_ADMIN, UserRoles.MODERATOR)
  @Delete(':id')
  async deleteModel(@Param() params: { id: string }) {
    return this.modelService.deleteModel(params.id);
  }
}
