import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpStatus,
} from '@nestjs/common';
import { Response } from 'express';
import { Prisma } from '@prisma/client';

@Catch(Prisma.PrismaClientKnownRequestError)
export class PrismaExceptionFilter implements ExceptionFilter {
  catch(exception: Prisma.PrismaClientKnownRequestError, host: ArgumentsHost) {
    const context = host.switchToHttp();
    const response = context.getResponse<Response>();

    let statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Something went wrong with <PERSON>rism<PERSON>';

    switch (exception.code) {
      case 'P2002': // Unique constraint failed
        statusCode = HttpStatus.BAD_REQUEST;
        message = 'Duplicate entry found.';
        break;
      case 'P2025': // Record to update not found
        statusCode = HttpStatus.NOT_FOUND;
        message = 'Resource not found.';
        break;
    }

    response.status(statusCode).json({
      statusCode,
      message,
      error: exception.meta?.cause || 'Database Error',
    });
  }
}
