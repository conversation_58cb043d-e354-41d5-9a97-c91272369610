{"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testEnvironment": "node", "testRegex": ".e2e-spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "moduleNameMapper": {"@app/llm/(.*)": "<rootDir>/../libs/llm/src/$1", "@app/llm": "<rootDir>/../libs/llm/src", "@openai/openai/(.*)": "<rootDir>/../libs/openai/src/$1", "@openai/openai": "<rootDir>/../libs/openai/src", "@app/fingerprint-js/(.*)": "<rootDir>/../libs/fingerprint-js/src/$1", "@app/fingerprint-js": "<rootDir>/../libs/fingerprint-js/src", "@app/ayrshare/(.*)": "<rootDir>/../libs/ayrshare/src/$1", "@app/ayrshare": "<rootDir>/../libs/ayrshare/src", "@app/ghl/(.*)": "<rootDir>/../libs/ghl/src/$1", "@app/ghl": "<rootDir>/../libs/ghl/src"}}