import { Injectable, NotFoundException } from '@nestjs/common';
import { ChatMessageRole } from '@prisma/client';
import { v4 as uuidV4 } from 'uuid';
import { PrismaService } from 'src/prisma.service';

@Injectable()
export class ChatMessageService {
  constructor(private readonly prisma: PrismaService) {}

  async saveMessage(
    userId: string,
    agentId: string,
    role: ChatMessageRole,
    content: string,
    fileData?: {
      fileName: string;
      fileType: string;
      fileSize: string;
      fileContent: string;
    }[],
  ) {
    try {
      const chatMessage = await this.prisma.chatMessage.findUnique({
        where: { userId_agentId: { userId, agentId } },
      });

      if (!chatMessage) {
        const newChatMessage = await this.prisma.chatMessage.create({
          data: {
            userId,
            agentId,
          },
        });

        const userChatMessage = await this.prisma.userChatMessage.create({
          data: {
            role,
            content: content.trim(),
            chatMessageId: newChatMessage.id,
          },
        });

        if (fileData?.length > 0) {
          for (const file of fileData) {
            await this.createMessageFile(
              userChatMessage.id,
              file.fileName,
              file.fileType,
              file.fileSize,
              file.fileContent,
            );
          }
        }

        return newChatMessage;
      }

      const chatMessageUpdate = await this.prisma.userChatMessage.create({
        data: {
          role,
          content: content.trim(),
          chatMessageId: chatMessage.id,
        },
      });

      if (fileData?.length > 0) {
        for (const file of fileData) {
          await this.createMessageFile(
            chatMessageUpdate.id,
            file.fileName,
            file.fileType,
            file.fileSize,
            file.fileContent,
          );
        }
      }

      return chatMessageUpdate;
    } catch (error) {
      console.log(error);
      if (error instanceof NotFoundException) throw error;
    }
  }

  async createMessageFile(
    userChatMessageId: string,
    fileName: string,
    fileType: string,
    fileSize: string,
    fileContent: string,
  ) {
    return await this.prisma.chatMessageFile.create({
      data: {
        userChatMessageId,
        fileName,
        fileType,
        fileSize,
        fileContent,
      },
    });
  }

  async getChatHistory(userId: string, agentId: string) {
    const chatSession = await this.prisma.chatMessage.findUnique({
      where: { userId_agentId: { userId, agentId } },
      include: {
        messages: {
          include: {
            files: true,
          },
        },
      },
    });

    const chatHistory = chatSession?.messages?.map?.((message) => ({
      id: message.id,
      role: message.role,
      content: message.content,
      experimental_attachments: message?.files?.map?.((file) => {
        const base64Content = Buffer.from(file.fileContent, 'utf-8').toString(
          'base64',
        );
        const dataUrl = `data:${file.fileType};base64,${base64Content}`;

        return {
          name: file.fileName,
          contentType: file.fileType,
          url: dataUrl,
        };
      }),
    }));

    return chatHistory ?? [];
  }

  async getCustomerChats({
    skip = 0,
    take = 10,
  }: {
    skip?: number;
    take?: number;
  }) {
    return await this.prisma.chatMessage.findMany({
      orderBy: { id: 'desc' },
      select: {
        messages: true,
      },
      skip,
      take,
    });
  }

  async resetChatThread(userId: string, agentId: string) {
    try {
      const chatMessage = await this.prisma.chatMessage.findUnique({
        where: { userId_agentId: { userId, agentId } },
        select: {
          id: true,
          messages: {
            select: {
              id: true,
            },
          },
        },
      });

      if (!chatMessage) {
        throw new NotFoundException('Chat message not found');
      }

      const userChatMessageIds = chatMessage.messages.map((msg) => msg.id);

      if (!(userChatMessageIds.length > 0)) {
        throw new NotFoundException('User chat messages not found');
      }

      await this.prisma.userChatMessage.deleteMany({
        where: {
          id: {
            in: userChatMessageIds,
          },
        },
      });

      await this.prisma.chatMessageFile.deleteMany({
        where: {
          userChatMessageId: {
            in: userChatMessageIds,
          },
        },
      });

      return { message: 'Chat thread reset' };
    } catch (error) {
      if (error instanceof NotFoundException) throw error;
    }
  }
}
