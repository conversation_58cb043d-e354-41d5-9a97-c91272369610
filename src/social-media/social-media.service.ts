import { Injectable, Logger } from '@nestjs/common';
import { <PERSON><PERSON> } from '@nestjs/schedule';

@Injectable()
export class SocialMediaService {
  private readonly logger = new Logger(SocialMediaService.name);

  @Cron('0 10 * * * *')
  async handleCronForGetSocialMediaPosts() {
    this.logger.debug('---START GET SOCIAL MEDIA POSTS---');
    this.logger.debug('---END GET SOCIAL MEDIA POSTS---');
  }
}
