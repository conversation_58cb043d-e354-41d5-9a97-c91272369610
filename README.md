# AI Chatbot Backend - Central Park Puppies :speech_balloon: :robot:

## Overview
This repository contains the backend application for the AI chatbot of Central Park Puppies, built using NestJS. The application integrates with multiple third-party services, provides authentication and authorization, and includes AI-driven features such as social media comment automation and conversation handling.

## Tech Stack
- **NestJS** - Backend framework
- **Prisma ORM** - Database management
- **MongoDB Atlas** - Vector storage
- **OpenAI API** - AI Assistant
- **FingerprintJS** - User tracking
- **GHL API Client** - Third-party integration
- **Ayreshare API Client** - Social media automation

## Features
1. **Backend Structure** - Initial setup of NestJS framework.
2. **Database Configuration** - Prisma ORM integration.
3. **AI and Security Libraries** - OpenAI and FingerprintJS configuration.
4. **Third-Party Integrations**:
   - GHL API client
   - Ayreshare API client
   - Scheduled fetch and processing of GHL conversations
5. **AI Processing**:
   - Embedding and vectorization module
   - Vector store to maintain conversations and knowledge base
6. **Social Media Automation**:
   - Scheduler to periodically fetch social media comments
   - AI-generated replies for social media comments
7. **AI Chatbot Core**:
   - OpenAI Assistant API development
8. **Authentication & Authorization**:
   - Secure user authentication and role-based access control
9. **Website Integration**:
   - Admin UI to update chatbot prompts, view and manage chatbot responses
   
## Installation
### Prerequisites
Ensure you have the following installed:
- Node.js (LTS version recommended)
- Docker (for MongoDB setup if needed)

### Setup
1. Clone the repository:
   ```sh
   git clone https://github.com/your-repo/cpp-chatbot.git
   cd cpp-chatbot
   ```
2. Install dependencies:
   ```sh
   pnpm install
   ```
3. Configure environment variables:
   Create a `.env` file and set the required environment variables:
   ```env
   DATABASE_URL=mongodb+srv://your-mongo-cluster-url
   OPENAI_API_KEY=your-openai-api-key
   FINGERPRINTJS_API_KEY=your-fingerprintjs-key
   ```
4. Generate Prisma client:
   ```sh
   npx prisma generate
   ```
5. Run the application:
   ```sh
   pnpm start:dev
   ```

## Docker Setup
1. Run the following command to build the multi stage docker image
   ```shell
   docker build -t cpp-chatbot-api .
   ```
2. After the image is created, execute the following command to run the image inside the container
   ```shell
   docker run --name cpp-chatbot-api-service -p 4000:4000 cpp-chatbot-api
   ```

## Contributing
1. Fork the repository.
2. Create a feature branch (`git checkout -b feature-name`).
3. Commit changes (`git commit -m 'Add feature'`).
4. Push to branch (`git push origin feature-name`).
5. Create a pull request.
