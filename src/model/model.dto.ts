import { <PERSON><PERSON><PERSON><PERSON> } from '@prisma/client';
import { IsEnum, IsNotEmpty, IsString } from 'class-validator';

export enum ModelName {
  GPT_4O = 'gpt-4o',
  GPT_4O_MINI = 'gpt-4o-mini',
  CLAUD_3_5_SONNET_LATEST = 'claude-3-5-sonnet-latest',
  CLAUD_3_7_SONNET_LATEST = 'claude-3-7-sonnet-latest',
  CLAUD_3_OPUS_LATEST = 'claude-3-opus-latest',
  GEMINI_1_5_PRO = 'gemini-1.5-pro',
  GEMINI_1_5_FLASH = 'gemini-1.5-flash',
  GEMINI_2_0_PRO_PREVIEW_TTS = 'gemini-2.5-pro-preview-tts',
  GEMINI_2_0_FLASH = 'gemini-2.0-flash',
}

export class ModelDto {
  @IsEnum(ModelName)
  @IsNotEmpty()
  name: ModelName;

  @IsEnum(LLMProvider)
  @IsNotEmpty()
  provider: LLMProvider;

  @IsString()
  @IsNotEmpty()
  label: string;

  @IsString()
  @IsNotEmpty()
  description: string;

  @IsString()
  @IsNotEmpty()
  key: string;
}
