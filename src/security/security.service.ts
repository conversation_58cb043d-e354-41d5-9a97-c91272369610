import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createCipheriv, scrypt, randomBytes, createDecipheriv } from 'crypto';
import { promisify } from 'util';

const scryptAsync = promisify(scrypt);

@Injectable()
export class SecurityService {
  private logger = new Logger(SecurityService.name);

  constructor(private readonly configService: ConfigService) {}

  async encrypt(data: string): Promise<string> {
    this.logger.log('---ENCRYPTING---');
    const password = this.configService.get<string>('security.encryption_key');
    const key = (await scryptAsync(password, 'salt', 32)) as Buffer;
    const iv = randomBytes(16); // New IV each time

    const cipher = createCipheriv('aes-256-cbc', key, iv);
    const encrypted = Buffer.concat([
      cipher.update(data, 'utf-8'),
      cipher.final(),
    ]);

    return iv.toString('hex') + ':' + encrypted.toString('hex');
  }

  async decrypt(encryptedData: string): Promise<string> {
    this.logger.log('---DECRYPTING---');
    const password = this.configService.get<string>('security.encryption_key');
    const key = (await scryptAsync(password, 'salt', 32)) as Buffer;

    const [ivHex, encryptedHex] = encryptedData.split(':');
    if (!ivHex || !encryptedHex) {
      throw new Error('Invalid encrypted data format');
    }

    const iv = Buffer.from(ivHex, 'hex');
    const encryptedText = Buffer.from(encryptedHex, 'hex');

    const decipher = createDecipheriv('aes-256-cbc', key, iv);
    const decrypted = Buffer.concat([
      decipher.update(encryptedText),
      decipher.final(),
    ]);

    return decrypted.toString('utf-8');
  }
}
