import { ChatAnthropic } from '@langchain/anthropic';
import { ChatGoogleGenerativeAI } from '@langchain/google-genai';
import { ChatOpenAI } from '@langchain/openai';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export type ModelProvider = 'openai' | 'anthropic' | 'gemini';

interface ModelConfig {
  provider: ModelProvider;
  key: string;
  model: string;
  temperature?: number;
  streaming?: boolean;
  tools?: any[];
}

@Injectable()
export class ModelFactory {
  constructor(private readonly configService: ConfigService) {}

  getLLM(config: ModelConfig) {
    const { provider, model, temperature, streaming, tools, key } = config;
    let llm: ChatOpenAI | ChatAnthropic | ChatGoogleGenerativeAI;

    switch (provider) {
      case 'openai':
        llm = new ChatOpenAI({
          apiKey: key,
          model,
          temperature,
          streaming,
        });
        break;
      case 'anthropic':
        llm = new ChatAnthropic({
          apiKey: key,
          model,
          temperature,
          streaming,
        });
        break;
      case 'gemini':
        llm = new ChatGoogleGenerativeAI({
          apiKey: key,
          model,
          temperature,
          streaming,
        });
        break;
      default:
        throw new Error('Invalid model provider');
    }

    return tools ? llm.bindTools(tools) : llm;
  }
}
