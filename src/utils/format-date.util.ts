export const formatDate = (date: Date): string => {
  const pad = (num: number) => String(num).padStart(2, '0');

  const seconds = pad(date.getSeconds());
  const minutes = pad(date.getMinutes());
  const hours = pad(date.getHours());
  const day = pad(date.getDate());
  const month = pad(date.getMonth() + 1);
  const year = date.getFullYear();

  return `${seconds}:${minutes}:${hours} ${day}-${month}-${year}`;
};
