import { Injectable, NotFoundException } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { PrismaService } from 'src/prisma.service';
import { SecurityService } from 'src/security/security.service';
import { ModelDto } from './model.dto';

@Injectable()
export class ModelService {
  constructor(
    private readonly prisma: PrismaService,
    private security: SecurityService,
  ) {}

  async createModel(data: Prisma.LLMModelCreateInput) {
    data.key = await this.security.encrypt(data.key);
    return await this.prisma.lLMModel.create({
      data,
    });
  }

  async getModels({ skip = 0, take = 10 }: { skip?: number; take?: number }) {
    const models = await this.prisma.lLMModel.findMany({
      skip,
      take,
    });

    return Promise.all(
      models.map(async (model) => {
        const key = await this.security.decrypt(model.key);
        return { ...model, key };
      }),
    );
  }

  async setModelKey(id: string, modelKey: string) {
    return await this.prisma.lLMModel.update({
      where: { id },
      data: {
        key: await this.security.encrypt(modelKey),
      },
    });
  }

  async getModel(id: string) {
    try {
      const model = await this.prisma.lLMModel.findUniqueOrThrow({
        where: {
          id,
        },
      });

      const key = await this.security.decrypt(model.key);
      return { ...model, key };
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException('Model not found');
      }
    }
  }

  async updateModel(id: string, data: ModelDto) {
    if (data.key) {
      data.key = await this.security.encrypt(data.key);
    }

    return await this.prisma.lLMModel.update({
      where: { id },
      data,
    });
  }

  async deleteModel(id: string) {
    return await this.prisma.lLMModel.delete({
      where: { id },
    });
  }
}
