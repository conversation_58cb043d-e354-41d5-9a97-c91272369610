import { InternalSystemPromptType } from '@prisma/client';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';

export class InternalSystemPromptDto {
  @IsString()
  @IsOptional()
  id?: string;

  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsString()
  prompt: string;

  @IsNotEmpty()
  @IsEnum(InternalSystemPromptType)
  type: InternalSystemPromptType;

  @IsString()
  description?: string;
}
