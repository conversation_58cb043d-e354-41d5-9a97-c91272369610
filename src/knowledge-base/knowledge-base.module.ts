import { <PERSON>du<PERSON> } from '@nestjs/common';
import { KnowledgeBaseController } from './knowledge-base.controller';
import { KnowledgeBaseService } from './knowledge-base.service';
import { VectorStoreService } from 'src/vector-store/vector-store.service';
import { AgentManageService } from 'src/agent/agent.manage.service';
import { PrismaService } from 'src/prisma.service';

@Module({
  controllers: [KnowledgeBaseController],
  providers: [
    KnowledgeBaseService,
    VectorStoreService,
    AgentManageService,
    PrismaService,
  ],
})
export class KnowledgeBaseModule {}
