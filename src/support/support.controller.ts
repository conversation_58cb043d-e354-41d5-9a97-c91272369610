import {
  Body,
  Controller,
  Post,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { SupportService } from './support.service';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';

@Controller('support')
export class SupportController {
  constructor(private readonly supportService: SupportService) {}

  @Post('/ticket')
  @UseInterceptors(FileInterceptor('file'))
  async sendEmail(
    @Body() body: { name: string; description: string },
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.supportService.sendEmail(body.name, body.description, file);
  }
}
