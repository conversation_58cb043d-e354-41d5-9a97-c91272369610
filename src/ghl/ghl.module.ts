import { Module } from '@nestjs/common';
import { GhlService } from './ghl.service';
import { GhlController } from './ghl.controller';
import { GhlModule } from '@app/ghl';

@Module({
  imports: [
    GhlModule.forRoot({
      privateToken: process.env.HIGHLEVEL_PRIVATE_TOKEN,
      accessType: 'Sub-Account',
      subAccountId: process.env.SUB_ACCOUNT_ID,
      scopes: [
        'contacts.readonly',
        'conversations.readonly',
        'conversations/message.readonly',
      ],
    }),
  ],
  providers: [GhlService],
  controllers: [GhlController],
})
export class GhlServiceModule {}
