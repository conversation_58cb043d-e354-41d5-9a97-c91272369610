import { Inject, Injectable, Logger } from '@nestjs/common';
import { GhlClientService } from './ghl.client.service';
import { IGhlConfig } from './ghl.config.interface';

@Injectable()
export class GhlContactsService {
  private logger = new Logger(GhlContactsService.name);

  constructor(
    private readonly ghlClientService: GhlClientService,
    @Inject('GHL_MODULE_OPTIONS') private options: IGhlConfig,
  ) {}

  async getContacts({ limit = 10 }: { limit?: number }) {
    try {
      this.logger.log('Getting contacts from HighLevel', { limit });
      const client = await this.ghlClientService.getClient();
      const { data, error } = await client.contacts.GET('/contacts/', {
        params: {
          query: {
            locationId: this.options.subAccountId,
            limit,
          },
        },
      });

      if (error) throw error;

      return data.contacts;
    } catch (error) {
      this.logger.error('Error getting contacts from HighLevel', error);
      throw error;
    }
  }
}
