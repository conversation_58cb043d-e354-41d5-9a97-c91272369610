import { Inject, Injectable } from '@nestjs/common';
import { IGhlConfig } from './ghl.config.interface';
import {
  createHighLevelClient,
  HighLevelIntegrationClient,
} from '@gnosticdev/highlevel-sdk';

@Injectable()
export class GhlClientService {
  private client: HighLevelIntegrationClient<any>;

  constructor(@Inject('GHL_MODULE_OPTIONS') private options: IGhlConfig) {}

  getClient() {
    if (!this.client) {
      this.client = createHighLevelClient({}, 'integration', {
        privateToken: this.options.privateToken,
        accessType: this.options.accessType,
        scopes: this.options.scopes as any,
      });
    }

    return this.client;
  }
}
