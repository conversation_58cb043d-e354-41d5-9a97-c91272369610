import { ResponseMode } from '@prisma/client';
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsString,
} from 'class-validator';

export class AgentDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsString()
  modelId: string;

  @IsNumber()
  @IsNotEmpty()
  temperature: number;

  @IsNotEmpty()
  @IsString()
  systemPrompt: string;

  @IsBoolean()
  cacheEnabled: boolean;

  @IsNotEmpty()
  @IsEnum(ResponseMode)
  responseMode: ResponseMode;
}
