// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

////////////////////////////////////////////////////////////////////////
//////////////////////////////// Enums /////////////////////////////////
////////////////////////////////////////////////////////////////////////

enum UserRoles {
  SUPER_ADMIN
  ADMIN
  MODERATOR
  PUBLIC_USER
}

enum ReasoningEffort {
  low
  medium
  high
}

enum ResponseFormat {
  auto
  text
  json_object
}

enum ChunkingStrategyType {
  auto
  static
}

enum ToolType {
  code_interpreter
  file_search
  function
}

enum ChatMessageRole {
  user
  assistant
}

enum SocialMediaPlatform {
  facebook
  instagram
  linkedin
  tiktok
  youtube
  bluesky
  pinterest
  twitter
}

enum LLMProvider {
  openai
  anthropic
  gemini
}

enum ResponseMode {
  stream
  polling
}

enum InternalSystemPromptType {
  summarize
  grade_documents
  agent
  rewrite
  generate
}

////////////////////////////////////////////////////////////////////////
//////////////////////////////// Models ////////////////////////////////
////////////////////////////////////////////////////////////////////////

model User {
  id           String        @id @default(auto()) @map("_id") @db.ObjectId
  firstName    String
  lastName     String?
  email        String?       @unique
  phoneNumber  String?
  userName     String        @unique
  password     String
  role         UserRoles
  chatMessages ChatMessage[]

  @@map("users")
}

model ChatMessage {
  id       String            @id @default(auto()) @map("_id") @db.ObjectId
  userId   String            @db.ObjectId
  agentId  String            @map("agent_id") @db.ObjectId
  agent    AgentConfig?      @relation(fields: [agentId], references: [id])
  user     User              @relation(fields: [userId], references: [id])
  messages UserChatMessage[]

  @@unique([userId, agentId])
  @@map("chat-messages")
}

model UserChatMessage {
  id            String            @id @default(auto()) @map("_id") @db.ObjectId
  role          ChatMessageRole
  content       String
  chatMessageId String            @db.ObjectId
  chatMessage   ChatMessage       @relation(fields: [chatMessageId], references: [id])
  files         ChatMessageFile[]

  @@map("user-chat-messages")
}

model ChatMessageFile {
  id                String          @id @default(auto()) @map("_id") @db.ObjectId
  userChatMessageId String          @db.ObjectId
  userChatMessage   UserChatMessage @relation(fields: [userChatMessageId], references: [id])
  fileName          String
  fileType          String
  fileSize          String
  fileContent       String

  @@map("chat-message-files")
}

model SocialMediaPost {
  id            String               @id @default(auto()) @map("_id") @db.ObjectId
  postId        String               @map("post_id")
  createdAt     DateTime             @map("created_at")
  post          String
  postUrl       String               @map("post_url")
  commentsCount Int                  @map("comments_count")
  platformTag   SocialMediaPlatform  @map("platform_tag")
  comments      SocialMediaComment[]

  @@index([postId], map: "post_id_index")
  @@map("social-media-posts")
}

model SocialMediaComment {
  id          String                    @id @default(auto()) @map("_id") @db.ObjectId
  commentId   String                    @map("comment_id")
  comment     String
  createdAt   DateTime                  @map("created_at")
  platformTag SocialMediaPlatform       @map("platform_tag")
  replies     SocialMediaCommentReply[]
  postId      String                    @map("post_id") @db.ObjectId
  post        SocialMediaPost           @relation(fields: [postId], references: [id])

  @@index([commentId], map: "comment_id_index")
  @@map("social-media-comments")
}

model LLMModel {
  id          String        @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  key         String
  label       String?
  description String?
  provider    LLMProvider
  agents      AgentConfig[]

  @@map("llm-models")
}

model AgentConfig {
  id           String        @id @default(auto()) @map("_id") @db.ObjectId
  name         String        @unique
  modelId      String        @map("model_id") @db.ObjectId
  model        LLMModel      @relation(fields: [modelId], references: [id])
  temperature  Float
  systemPrompt String        @map("system_prompt")
  cacheEnabled Boolean?      @default(false) @map("cache_enabled")
  chatMessages ChatMessage[]
  responseMode ResponseMode  @default(polling) @map("response_mode")
  default      Boolean       @default(true)
  createdAt    DateTime      @default(now()) @map("created_at")
  updatedAt    DateTime      @updatedAt @map("updated_at")

  @@map("agent-configs")
}

model Documents {
  id        String  @id @default(auto()) @map("_id") @db.ObjectId
  name      String
  content   String
  agentId   String
  fileName  String? @map("filename")
  mimeType  String? @map("mimetype")
  embedding Float[]
  loc       Json?
  metadata  Json?

  @@map("documents")
}

model InternalSystemPrompts {
  id          String                   @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  prompt      String
  type        InternalSystemPromptType
  description String?

  @@map("internal-system-prompts")
}

///////////////////////////////////////////////////////////////////////
//////////////////////////////// Types ////////////////////////////////
///////////////////////////////////////////////////////////////////////

type Tool {
  type ToolType
}

type ToolResources {
  code_interpreter CodeInterpreter?
  file_search      FileSearch?
}

type CodeInterpreter {
  file_ids String[] @default([])
}

type FileSearch {
  vector_store_ids String[] @default([])
}

type VectorStore {
  file_ids          String[]          @default([])
  chunking_strategy ChunkingStrategy?
}

type ChunkingStrategy {
  type   ChunkingStrategyType
  static StaticChunking?
}

type StaticChunking {
  max_chunk_size_tokens Int
  chunk_overlap_tokens  Int
}

type SocialMediaCommentReply {
  commentId   String
  comment     String
  createdAt   DateTime
  platformTag SocialMediaPlatform
}
