import { Modu<PERSON> } from '@nestjs/common';
import { RagAgentService } from './rag-agent.service';
import { PrismaService } from 'src/prisma.service';
import { VectorStoreService } from 'src/vector-store/vector-store.service';
import { GraphConfig } from './graph.config';
import { PromptService } from './prompt.service';
import { ModelFactory } from './model.factory';
import { ToolsModule } from 'src/tools/tools.module';
import { AgentManageService } from 'src/agent/agent.manage.service';
import { ChatMessageModule } from 'src/chat-message/chat-message.module';
import { AbortModule } from 'src/abort/abort.module';

@Module({
  imports: [ToolsModule, ChatMessageModule, AbortModule],
  providers: [
    RagAgentService,
    PrismaService,
    VectorStoreService,
    GraphConfig,
    PromptService,
    ModelFactory,
    AgentManageService,
  ],
  exports: [RagAgentService],
})
export class RagAgentModule {}
