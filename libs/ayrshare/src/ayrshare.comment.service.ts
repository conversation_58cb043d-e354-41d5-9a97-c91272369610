import {
  Inject,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import {
  IAyrShareConfig,
  ISocialMediaComment,
  ISocialMediaPost,
  Platform,
} from './ayrshare.config.interface';

@Injectable()
export class AyrShareCommentService {
  constructor(
    @Inject('AYRSHARE_MODULE_OPTIONS') private options: IAyrShareConfig,
  ) {}

  async getCommentsForPost(
    postId: string,
    platform: Platform,
  ): Promise<ISocialMediaComment[]> {
    const commentResponse = await this.fetchCommentsForPost(postId, platform);
    const comments: any[] = commentResponse;
    const formattedFacebookPostComments: ISocialMediaComment[] = [];

    if (!comments.length) {
      return formattedFacebookPostComments;
    }

    for (const comment of comments) {
      formattedFacebookPostComments.push({
        commentId: comment.commentId,
        comment: comment.comment,
        createdAt: new Date(comment.created),
        platformTag: platform,
        replies: comment?.replies?.map?.((reply) => {
          return {
            commentId: reply.commentId,
            comment: reply.comment,
            createdAt: new Date(reply.created),
            platformTag: platform,
          };
        }),
      });
    }

    return formattedFacebookPostComments;
  }

  private async fetchCommentsForPost(postId: string, platform: Platform) {
    const response = await fetch(
      `${this.options.baseUrl}/comments/${postId}?searchPlatformId=true&platform=${platform}`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${this.options.apiKey}`,
        },
      },
    );

    if (!response.ok) {
      throw new InternalServerErrorException(
        `Exception with fetching comments for post Id: ${postId}`,
      );
    }

    return await response.json();
  }
}
