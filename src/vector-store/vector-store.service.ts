import { Injectable } from '@nestjs/common';
import { MongoDBAtlasVectorSearch } from '@langchain/mongodb';
import { Collection, MongoClient } from 'mongodb';
import { ConfigService } from '@nestjs/config';
import { OpenAIEmbeddings } from '@langchain/openai';

@Injectable()
export class VectorStoreService {
  private client: MongoClient;
  private collection: Collection;
  private vectorStore: MongoDBAtlasVectorSearch;
  private embedding: OpenAIEmbeddings;

  constructor(private readonly configService: ConfigService) {
    const openAIKey = this.configService.get<string>('llm.openai_api_key');
    const connectionUrl = this.configService.get<string>(
      'database.mongodb_atlas_url',
    );
    const dbName = this.configService.get<string>(
      'database.mongodb_atlas_db_name',
    );
    const collectionName = this.configService.get<string>(
      'database.mongodb_atlas_collection_name',
    );

    this.client = new MongoClient(connectionUrl);
    this.collection = this.client.db(dbName).collection(collectionName);

    this.embedding = new OpenAIEmbeddings({
      model: 'text-embedding-3-large',
      apiKey: openAIKey,
    });

    this.vectorStore = new MongoDBAtlasVectorSearch(this.embedding, {
      collection: this.collection,
      indexName: 'vector_index',
      textKey: 'content',
      embeddingKey: 'embedding',
    });
  }

  getVectorStore(): MongoDBAtlasVectorSearch {
    return this.vectorStore;
  }

  getEmbedding(): OpenAIEmbeddings {
    return this.embedding;
  }

  getMongoDbClient(): MongoClient {
    return this.client;
  }
}
