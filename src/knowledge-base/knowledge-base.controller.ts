import { Body, Controller, Get, Param, Patch, Query } from '@nestjs/common';
import { KnowledgeBaseService } from './knowledge-base.service';
import { UpdateKnowledgeBaseDto } from './knowledge-base.dto';

@Controller('knowledge-base')
export class KnowledgeBaseController {
  constructor(private readonly knowledgeBaseService: KnowledgeBaseService) {}

  @Patch()
  async updateKnowledgeBase(@Body() body: UpdateKnowledgeBaseDto) {
    return this.knowledgeBaseService.updateKnowledgeBase(
      body.name,
      body.description,
      body.agentId,
      body.id,
    );
  }

  @Get(':agentId')
  async getKnowledgeBase(
    @Param('agentId') agentId: string,
    @Query('query') query: string,
  ) {
    return this.knowledgeBaseService.getKnowledgeBase(agentId, query);
  }

  @Get('/documents/:agentId')
  async getKnowledgeBaseDocuments(@Param('agentId') agentId: string) {
    return this.knowledgeBaseService.getKnowledgeBaseDocuments(agentId);
  }
}
