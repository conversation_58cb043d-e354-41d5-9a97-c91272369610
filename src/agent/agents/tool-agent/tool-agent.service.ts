import { Injectable, Logger } from '@nestjs/common';
import { Tool } from '@langchain/core/tools';
import { ChatOpenAI } from '@langchain/openai';
import { RetrieveTool } from 'src/tools/retrieve-tool/retrieve-tool';
import { ConfigService } from '@nestjs/config';
import { AIMessage, HumanMessage } from '@langchain/core/messages';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { createToolCallingAgent } from 'langchain/agents';
import { AgentExecutor } from 'langchain/agents';
import { systemPrompt } from './prompt';
import { ChatMessageService } from 'src/chat-message/chat-message.service';
import { ChatMessageRole } from '@prisma/client';
import { ModelWithTools, OpenAIModel } from 'src/agent/types';

@Injectable()
export class ToolAgentService {
  private logger = new Logger(ToolAgentService.name);
  private tools: Tool[];
  private model: OpenAIModel;
  private modelWithTools: ModelWithTools;
  private prompt: ChatPromptTemplate;
  private agent;
  private agentExecuter: AgentExecutor;

  constructor(
    private readonly retrieveTool: RetrieveTool,
    private readonly configService: ConfigService,
    private readonly chatMessageService: ChatMessageService,
  ) {
    this.initialize();
  }

  private async initialize() {
    this.tools = this.initializeTools();
    this.model = this.initializeModel();
    this.modelWithTools = this.initializeModelWithTool();
    this.prompt = this.initializedPrompt();
    this.agent = this.initializeAgent();
    this.agentExecuter = this.initializeAgentExecuter();
  }

  private initializeTools(): any[] {
    return [this.retrieveTool.getTool()];
  }

  private initializeModel(): OpenAIModel {
    return new ChatOpenAI({
      apiKey: this.configService.get<string>('llm.openai_api_key'),
      model: 'gpt-4o-mini',
      temperature: 1,
    });
  }

  private initializeModelWithTool(): ModelWithTools {
    return this.model.bindTools(this.tools);
  }

  private initializedPrompt(): ChatPromptTemplate {
    return ChatPromptTemplate.fromMessages([
      ['system', systemPrompt],
      ['placeholder', '{chat_history}'],
      ['human', '{input}'],
      ['placeholder', '{agent_scratchpad}'],
    ]);
  }

  private initializeAgent() {
    return createToolCallingAgent({
      llm: this.model,
      tools: this.tools,
      prompt: this.prompt,
    });
  }

  private initializeAgentExecuter() {
    return new AgentExecutor({
      agent: this.agent,
      tools: this.tools,
    });
  }

  async responseWithToolCalls(input: string) {
    const responseWithToolCalls = await this.modelWithTools.invoke([
      new HumanMessage(input),
    ]);

    this.logger.debug(`Content: ${responseWithToolCalls.content}`);
    this.logger.debug(
      `Tool calls: ${JSON.stringify(responseWithToolCalls.tool_calls, null, 2)}`,
    );

    return responseWithToolCalls;
  }

  async generateAnswer(userId: string, sessionId: string, input: string) {
    const chatHistory = await this.chatMessageService.getChatHistory(
      userId,
      '',
    );
    const messages = chatHistory.map((message) =>
      message.role === ChatMessageRole.user
        ? new HumanMessage(message.content)
        : new AIMessage(message.content),
    );

    messages.push(new HumanMessage(input));

    const agentAnswer = await this.agentExecuter.invoke({
      input,
      chat_history: messages,
    });

    await this.chatMessageService.saveMessage(
      userId,
      sessionId,
      ChatMessageRole.user,
      input,
    );
    await this.chatMessageService.saveMessage(
      userId,
      sessionId,
      ChatMessageRole.assistant,
      agentAnswer.output,
    );

    return agentAnswer;
  }
}
