import { Injectable } from '@nestjs/common';
import { VectorStoreService } from 'src/vector-store/vector-store.service';
import { tool } from '@langchain/core/tools';
import { z } from 'zod';

@Injectable()
export class RetrieveTool {
  constructor(private readonly vectorStoreService: VectorStoreService) {}

  public getTool(agentId?: string) {
    return tool(
      async (input: string, config) => {
        const retriever = this.vectorStoreService
          .getVectorStore()
          .asRetriever({ k: 3, filter: agentId ? { agentId } : undefined });
        const docs = await retriever.invoke(input, config);
        return docs.map((doc) => doc.pageContent).join('\n\n');
      },
      {
        name: 'retrieve_tool',
        description:
          'Use this tool when need to retrieve details from the company knowledge base documents',
        schema: z.string(),
      },
    );
  }
}
