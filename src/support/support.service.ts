import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodeMailer from 'nodemailer';
import * as fs from 'fs';
import * as Handlebars from 'handlebars';
import * as path from 'path';

@Injectable()
export class SupportService {
  private transporter: nodeMailer.Transporter;

  constructor(private readonly configService: ConfigService) {
    this.transporter = nodeMailer.createTransport({
      service: 'gmail',
      auth: {
        type: 'OAuth2',
        user: this.configService.get<string>('gmail.user'),
        clientId: this.configService.get<string>('gmail.client_id'),
        clientSecret: this.configService.get<string>('gmail.client_secret'),
        refreshToken: this.configService.get<string>('gmail.refresh_token'),
      },
    });
  }

  async sendEmail(
    subject: string,
    text: string,
    attachment: Express.Multer.File,
  ) {
    const htmlTemplate = fs.readFileSync(
      path.join(__dirname, 'templates/email-template.hbs'),
      'utf-8',
    );

    const compileTemplate = Handlebars.compile(htmlTemplate);
    const htmlContent = compileTemplate({
      subject,
      message: text,
      date: new Date().toLocaleDateString(),
    });

    const mailOptions = {
      from: this.configService.get<string>('gmail.user'),
      to: this.configService.get<string[]>('gmail.notify_emails'),
      subject: 'Centralized AI - Support Ticket',
      text,
      html: htmlContent,
      attachments: [
        {
          filename: attachment.filename,
          content: attachment.buffer,
          contentType: attachment.mimetype,
        },
      ],
    };

    await this.transporter.sendMail(mailOptions);
  }
}
