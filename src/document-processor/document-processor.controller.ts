import {
  Controller,
  Post,
  UseInterceptors,
  UploadedFiles,
  UseGuards,
  Req,
} from '@nestjs/common';
import { Request } from 'express';
import { FilesInterceptor } from '@nestjs/platform-express';
import { DocumentProcessorService } from './document-processor.service';
import { JwtAuthGuard } from 'src/auth/guards/jwt.guard';
import { RolesGuard } from 'src/auth/guards/role.guard';
import { UserRoles } from '@prisma/client';
import { Roles } from 'src/auth/roles/roles.decorator';

@Controller('document-processor')
export class DocumentProcessorController {
  constructor(
    private readonly documentProcessorService: DocumentProcessorService,
  ) {}

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRoles.ADMIN, UserRoles.SUPER_ADMIN, UserRoles.MODERATOR)
  @Post('/upload')
  @UseInterceptors(FilesInterceptor('files', 4))
  async uploadFiles(
    @UploadedFiles() files: Express.Multer.File[],
    @Req() request: Request,
  ) {
    const agentId = request.headers?.agentid as string;

    const documents = await this.documentProcessorService.processFiles(
      request.body.name,
      files,
      agentId,
    );
    return { message: 'Files processed successfully', documents };
  }
}
