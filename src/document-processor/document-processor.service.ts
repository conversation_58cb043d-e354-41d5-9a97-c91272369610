import { BadRequestException, Injectable } from '@nestjs/common';
import { Document } from 'langchain/document';
import { RecursiveCharacterTextSplitter } from '@langchain/textsplitters';
import { Readable } from 'stream';
import { VectorStoreService } from 'src/vector-store/vector-store.service';

const pdfParse = require('pdf-parse');

@Injectable()
export class DocumentProcessorService {
  constructor(private vectorStoreService: VectorStoreService) {}

  private cleanText(text: string): string {
    return text
      .replace(/[\u25CB\u25CF\u25A0\u2022\u2023\u25E6\u2043\u2219]/g, '') // ○ ● ■ and other bullets
      .replace(/[“”"']/g, '') // curly and straight quotes
      .replace(/^\s*[\d]+\.\s+/gm, '') // numbered lists like 1. 2. etc.
      .replace(/^\s*[-–—*]\s+/gm, '') // dashes, asterisks at line start
      .replace(/\r?\n|\r/g, ' ') // newlines to space
      .replace(/\s+/g, ' ') // collapse multiple spaces
      .trim();
  }

  async processFiles(
    name: string,
    files: Express.Multer.File[],
    agentId: string,
  ): Promise<Document[]> {
    try {
      if (!agentId) {
        throw new BadRequestException('Agent ID is required');
      }

      if (!files || files.length === 0) {
        throw new BadRequestException('No files uploaded');
      }

      const textSplitter = new RecursiveCharacterTextSplitter({
        chunkSize: 1000, // TODO: Needs to be configurable externally
        chunkOverlap: 100,
      });

      let documents: Document[] = [];

      for (const file of files) {
        let fileContent = '';

        if (file.mimetype === 'application/pdf') {
          const pdfData = await pdfParse(file.buffer);
          fileContent = pdfData.text;
        } else {
          const stream = Readable.from(file.buffer);
          fileContent = await this.streamToString(stream);
        }

        const cleanedText = this.cleanText(fileContent);

        const document = new Document({
          pageContent: cleanedText,
          metadata: {
            name,
            mimetype: file.mimetype,
            agentId,
          },
        });

        const splitDocs = await textSplitter.splitDocuments([document]);

        await this.vectorStoreService.getVectorStore().addDocuments(splitDocs);

        documents = documents.concat(splitDocs);
      }

      return documents;
    } catch (error) {
      throw error;
    }
  }

  private async streamToString(stream: Readable): Promise<string> {
    return new Promise((resolve, reject) => {
      let data = '';
      stream.on('data', (chunk) => (data += chunk));
      stream.on('end', () => resolve(data));
      stream.on('error', (err) => reject(err));
    });
  }
}
