import { Body, Controller, Get, Query } from '@nestjs/common';
import { SecurityService } from './security.service';

@Controller('security')
export class SecurityController {
  constructor(private securityService: SecurityService) {}

  @Get('/encrypt')
  async encrypt(@Body() body: { data: string }) {
    return this.securityService.encrypt(body.data);
  }

  @Get('/decrypt')
  async decrypt(@Body() body: { data: string }) {
    return this.securityService.decrypt(body.data);
  }
}
