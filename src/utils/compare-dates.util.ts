export const compareDates = (date1: Date, date2: Date) => {
  const diffMs = Math.abs(date1.getTime() - date2.getTime());

  const diffSeconds = Math.floor((diffMs / 1000) % 60);
  const diffMinutes = Math.floor((diffMs / (1000 * 60)) % 60);
  const diffHours = Math.floor((diffMs / (1000 * 60 * 60)) % 24);
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  return {
    milliseconds: diffMs,
    seconds: diffSeconds,
    minutes: diffMinutes,
    hours: diffHours,
    days: diffDays,
  };
};
