import { DynamicModule, Module } from '@nestjs/common';
import { GhlService } from './ghl.service';
import { IGhlConfig } from './ghl.config.interface';
import { GhlContactsService } from './ghl.contacts.service';
import { GhlClientService } from './ghl.client.service';

@Module({
  providers: [GhlService],
  exports: [GhlService],
})
export class GhlModule {
  static forRoot(options: IGhlConfig): DynamicModule {
    return {
      module: GhlModule,
      providers: [
        {
          provide: 'GHL_MODULE_OPTIONS',
          useValue: options,
        },
        GhlService,
        GhlClientService,
        GhlContactsService,
      ],
      exports: [GhlService, GhlContactsService, GhlClientService],
    };
  }
}
