import { Injectable } from '@nestjs/common';

@Injectable()
export class AbortService {
  private controllers = new Map<string, AbortController>();

  create(sessionId: string): AbortController {
    const controller = new AbortController();
    this.controllers.set(sessionId, controller);
    console.log('---CREATING---', sessionId, controller);
    return controller;
  }

  get(sessionId: string): AbortController | undefined {
    return this.controllers.get(sessionId);
  }

  abort(sessionId: string) {
    const controller = this.controllers.get(sessionId);
    console.log('---ABORTING---', sessionId, controller);
    if (controller) {
      controller.abort();
      this.controllers.delete(sessionId); // Optional cleanup
    }
  }

  remove(sessionId: string) {
    this.controllers.delete(sessionId);
  }
}
