import { BaseMessage } from '@langchain/core/messages';
import { MemorySaver, START, StateGraph } from '@langchain/langgraph';
import { ChatOpenAI } from '@langchain/openai';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { BaseAgentService } from '../base-agent.service';
import { ToolNode } from '@langchain/langgraph/prebuilt';

@Injectable()
export class ReactAgentService extends BaseAgentService {
  constructor(private readonly configService: ConfigService) {
    super();
    this.logger = new Logger(ReactAgentService.name);
  }

  async createAgent(
    name: string,
    systemPrompt: string,
    model: { name: string; temperature: number; cache: boolean },
    tools: any[],
  ) {
    if (this.agents.has(name)) {
      this.logger.debug(`---AGENT "${name}" ALREADY EXISTS`);
      return { agent: this.agents.get(name).agent };
    }

    this.graphState = this.initializeGraphState();
    this.tools = tools;
    this.toolNode = new ToolNode(this.tools);
    this.model = new ChatOpenAI({
      apiKey: this.configService.get<string>('llm.openai_api_key'),
      model: model.name,
      temperature: model.temperature,
      cache: model.cache,
    });
    this.modelWithTools = this.model.bindTools(this.tools);
    this.workflow = this.initializeWorkflow();
    this.checkPointer = new MemorySaver();
    this.prompt = systemPrompt;

    const agent = this.workflow.compile({ checkPointer: this.checkPointer });

    this.agents.set(name, { agent });
    return { agent };
  }

  protected initializeWorkflow() {
    return new StateGraph(this.graphState)
      .addNode('agent', this.callModel.bind(this))
      .addNode('tools', this.toolNode)
      .addEdge(START, 'agent')
      .addConditionalEdges('agent', this.shouldContinue.bind(this))
      .addEdge('tools', 'agent');
  }
}
