import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { GraphConfig } from './graph.config';
import { AIMessage, HumanMessage, ToolMessage } from '@langchain/core/messages';
import { AgentManageService } from 'src/agent/agent.manage.service';
import { AgentConfig, ChatMessageRole, LLMModel } from '@prisma/client';
import { ChatMessageService } from 'src/chat-message/chat-message.service';
import { OpenAIEmbeddings } from '@langchain/openai';
import { MemoryVectorStore } from 'langchain/vectorstores/memory';
import { Document } from 'langchain/document';
import { Readable } from 'stream';
import { ConfigService } from '@nestjs/config';
import { BaseRetriever } from '@langchain/core/retrievers';
import { RetrieveTool } from 'src/tools/retrieve-tool/retrieve-tool';
import { AbortService } from 'src/abort/abort.service';
import { SecurityService } from 'src/security/security.service';

const pdfParse = require('pdf-parse');

@Injectable()
export class RagAgentService {
  private logger = new Logger(RagAgentService.name);
  private typingConfig = {
    '|': 90,
    '>>': 0,
    '.': 120,
    '\n': 50,
  };

  constructor(
    private graphConfig: GraphConfig,
    private agentManageService: AgentManageService,
    private abortService: AbortService,
    private readonly chatMessageService: ChatMessageService,
    private readonly configService: ConfigService,
    private readonly retrieveTool: RetrieveTool,
    private readonly security: SecurityService,
  ) {}

  async generateCompleteAnswer(
    input: string,
    userId: string,
    sessionId: string,
    agentId: string,
    files: Express.Multer.File[],
  ) {
    try {
      const abortController = this.abortService.create(sessionId);
      const signal = abortController.signal;

      let fileRetriever = null;
      const messageFileData: {
        fileName: string;
        fileType: string;
        fileSize: string;
        fileContent: string;
      }[] = [];
      const messages = [
        new HumanMessage(
          input
            .split('\n') // Split by lines
            .map((line) => line.trim()) // Trim each line
            .filter((line) => line) // Remove empty lines
            .join(' ') // Join lines with a space
            .trim(),
        ),
      ];

      if (!agentId) {
        throw new BadRequestException('Agent ID is required');
      }

      this.logger.log(
        '---GENERATE COMPLETE ANSWER: SAVE USER MESSAGE TO DB---',
      );
      await this.chatMessageService.saveMessage(
        userId,
        agentId,
        ChatMessageRole.user,
        input,
        messageFileData,
      );

      if (files?.length) {
        const fileContents: string[] = [];

        for (const file of files) {
          let fileContent = '';

          if (file.mimetype === 'application/pdf') {
            const pdfData = await pdfParse(file.buffer);
            fileContent = pdfData.text;
          } else {
            const stream = Readable.from(file.buffer);
            fileContent = await this.streamToString(stream);
          }

          fileContents.push(fileContent);
          messageFileData.push({
            fileName: file.originalname,
            fileType: file.mimetype,
            fileSize: file.size.toString(),
            fileContent,
          });
        }

        const docs = fileContents.flatMap(
          (text) =>
            text
              .match(/(.|[\r\n]){1,1000}/g) // Split into chunks of ~1000 chars
              ?.map((chunk) => new Document({ pageContent: chunk })) || [],
        );

        const openAIKey = this.configService.get<string>('llm.openai_api_key');

        const embeddings = new OpenAIEmbeddings({
          model: 'text-embedding-3-large',
          apiKey: openAIKey,
        });
        const vectorStore = await MemoryVectorStore.fromDocuments(
          docs,
          embeddings,
        );
        fileRetriever = vectorStore.asRetriever();
      }

      let agentConfig: AgentConfig & { model: LLMModel };

      if (!agentConfig) {
        agentConfig = await this.agentManageService.getAgent({
          id: agentId,
        });
      } else {
        agentConfig = await this.agentManageService.getAgent({
          default: true,
        });
      }

      let chatHistory = [];

      if (!(files?.length > 0) && userId && sessionId) {
        chatHistory = await this.chatMessageService.getChatHistory(
          userId,
          agentId,
        );

        if (chatHistory?.length > 0) {
          chatHistory = chatHistory.map((chat) => {
            return chat.role === ChatMessageRole.user
              ? new HumanMessage(chat.content)
              : new AIMessage(chat.content);
          });

          const key = await this.security.decrypt(agentConfig.model.key);

          this.logger.log(
            '---generateCompleteAnswer: SUMMARIZING CHAT HISTORY---',
          );
          const summarizeAgent = this.graphConfig.buildSummarizationGraph({
            agentId: agentConfig.id,
            provider: agentConfig.model.provider,
            model: agentConfig.model.name,
            systemPrompt: agentConfig.systemPrompt,
            temperature: agentConfig.temperature,
            key,
          });

          const result = await summarizeAgent.invoke(
            {
              chat_history: chatHistory,
            },
            {
              signal,
            },
          );

          const lastMessage = result.messages[result.messages?.length - 1];
          this.logger.log(
            '---generateCompleteAnswer: SUMMARIZED CHAT HISTORY---',
            lastMessage.content,
          );
          chatHistory = [lastMessage];
        }
      }

      const key = await this.security.decrypt(agentConfig.model.key);

      const agent = this.graphConfig.buildAgentGraph({
        agentId: agentConfig.id,
        provider: agentConfig.model.provider,
        model: agentConfig.model.name,
        systemPrompt: agentConfig.systemPrompt,
        temperature: agentConfig.temperature,
        customRetriever: fileRetriever,
        key,
      });

      let result = await agent.invoke(
        {
          messages,
          chat_history: chatHistory,
        },
        {
          signal,
        },
      );

      while (true) {
        const last = result.messages[result.messages?.length - 1];

        if (
          last instanceof AIMessage &&
          last.tool_calls &&
          last.tool_calls?.length > 0
        ) {
          const toolResults = await Promise.all(
            last.tool_calls.map(async (toolCall) => {
              const toolOutput = await this.executeTool(
                toolCall.name,
                toolCall.args,
                fileRetriever,
                agentId,
                signal,
              ); // or config
              return new ToolMessage({
                content: toolOutput,
                tool_call_id: toolCall.id,
                name: toolCall.name,
              });
            }),
          );

          result = await agent.invoke(
            {
              messages: [...result.messages, ...toolResults],
              chat_history: chatHistory,
            },
            {
              signal,
            },
          );
        } else {
          break;
        }
      }

      const finalAnswerMessage = result.messages
        .reverse()
        .find((msg) => msg instanceof AIMessage && msg.content);

      const finalAnswer = finalAnswerMessage?.content ?? '';

      let processedAnswer = finalAnswer.toString();

      if (this.typingConfig['|'] !== undefined) {
        processedAnswer = processedAnswer.replace(/\|/g, '');
      }

      if (this.typingConfig['>>'] !== undefined) {
        processedAnswer = processedAnswer.replace(/>>/g, '');
      }

      this.logger.log(
        '---GENERATE COMPLETE ANSWER: SAVE AI AGENT ANSWER TO DB---',
      );
      await this.chatMessageService.saveMessage(
        userId,
        agentId,
        ChatMessageRole.assistant,
        processedAnswer,
      );

      return processedAnswer.trim();
    } catch (error) {
      if (error.name === 'AbortError' || error.message?.includes('aborted')) {
        this.logger.warn(`[ABORTED]: Answer generation aborted by user`);
        throw new BadRequestException('Answer generation aborted by user.');
      }
      this.logger.error(error);
      if (error instanceof NotFoundException) throw error;

      if (error instanceof BadRequestException) throw error;

      if (error instanceof InternalServerErrorException) throw error;
    }
  }

  private async executeTool(
    toolName: string,
    args: Record<string, any>,
    fileRetriever: BaseRetriever,
    agentId: string,
    signal?: AbortSignal,
  ): Promise<string> {
    if (signal?.aborted) {
      this.logger.warn('---TOOL ABORTED BEFORE EXECUTION---');
      throw new Error('Execution aborted');
    }

    let docs: any;

    if (toolName === 'uploaded_file_retriever') {
      docs = await fileRetriever.invoke(args.input);
      this.logger.log('---EXECUTE TOOL: UPLOADED FILE RETRIEVER---', docs);
    } else if (toolName === 'retrieve_tool') {
      docs = await this.retrieveTool.getTool(agentId).invoke(args.input);
      this.logger.log('---EXECUTE TOOL: RETRIEVE TOOL---', docs);
    } else {
      throw new Error(`Unknown tool: ${toolName}`);
    }

    if (typeof docs === 'string') {
      return docs;
    }

    if (Array.isArray(docs)) {
      return docs.map((d) => d.pageContent).join('\n');
    }

    this.logger.error('Unexpected tool return type:', docs);
    throw new Error('Tool invocation did not return valid content');
  }

  private async streamToString(stream: Readable): Promise<string> {
    return new Promise((resolve, reject) => {
      let data = '';
      stream.on('data', (chunk) => (data += chunk));
      stream.on('end', () => resolve(data));
      stream.on('error', (err) => reject(err));
    });
  }

  async *generateAnswer(
    input: string,
    userId: string,
    sessionId: string,
    agentId: string,
    files: Express.Multer.File[],
  ) {
    try {
      const abortController = this.abortService.create(sessionId);
      const signal = abortController.signal;

      let fileRetriever = null;
      const messageFileData: {
        fileName: string;
        fileType: string;
        fileSize: string;
        fileContent: string;
      }[] = [];
      const messages = [
        new HumanMessage(
          input
            .split('\n') // Split by lines
            .map((line) => line.trim()) // Trim each line
            .filter((line) => line) // Remove empty lines
            .join(' ') // Join lines with a space
            .trim(),
        ),
      ];

      if (!agentId) {
        throw new BadRequestException('Agent ID is required');
      }

      this.logger.log(
        '---GENERATE ANSWER (STREAM): SAVE USER MESSAGE TO DB---',
      );
      await this.chatMessageService.saveMessage(
        userId,
        agentId,
        ChatMessageRole.user,
        input,
        messageFileData,
      );

      if (files?.length) {
        const fileContents: string[] = [];

        for (const file of files) {
          let fileContent = '';

          if (file.mimetype === 'application/pdf') {
            const pdfData = await pdfParse(file.buffer);
            fileContent = pdfData.text;
          } else {
            const stream = Readable.from(file.buffer);
            fileContent = await this.streamToString(stream);
          }

          fileContents.push(fileContent);
          messageFileData.push({
            fileName: file.originalname,
            fileType: file.mimetype,
            fileSize: file.size.toString(),
            fileContent,
          });
        }

        const docs = fileContents.flatMap(
          (text) =>
            text
              .match(/(.|[\r\n]){1,1000}/g) // Split into chunks of ~1000 chars
              ?.map((chunk) => new Document({ pageContent: chunk })) || [],
        );

        const openAIKey = this.configService.get<string>('llm.openai_api_key');

        const embeddings = new OpenAIEmbeddings({
          model: 'text-embedding-3-large',
          apiKey: openAIKey,
        });
        const vectorStore = await MemoryVectorStore.fromDocuments(
          docs,
          embeddings,
        );
        fileRetriever = vectorStore.asRetriever();
      }

      let agentConfig: AgentConfig & { model: LLMModel };

      if (!agentConfig) {
        agentConfig = await this.agentManageService.getAgent({
          id: agentId,
        });
      } else {
        agentConfig = await this.agentManageService.getAgent({
          default: true,
        });
      }

      let chatHistory = [];

      if (userId && sessionId) {
        chatHistory = await this.chatMessageService.getChatHistory(
          userId,
          agentId,
        );

        if (chatHistory?.length > 0) {
          chatHistory = chatHistory.map((chat) => {
            return chat.role === ChatMessageRole.user
              ? new HumanMessage(chat.content)
              : new AIMessage(chat.content);
          });

          const key = await this.security.decrypt(agentConfig.model.key);

          this.logger.log(
            '---GENERATE ANSWER (STREAM): SUMMARIZING CHAT HISTORY---',
          );
          const summarizeAgent = this.graphConfig.buildSummarizationGraph({
            agentId: agentConfig.id,
            provider: agentConfig.model.provider,
            model: agentConfig.model.name,
            systemPrompt: agentConfig.systemPrompt,
            temperature: agentConfig.temperature,
            key,
          });

          const result = await summarizeAgent.invoke(
            {
              chat_history: chatHistory,
            },
            {
              signal,
            },
          );

          const lastMessage = result.messages[result.messages?.length - 1];
          this.logger.log(
            '---GENERATE ANSWER (STREAM): SUMMARIZED CHAT HISTORY---',
            lastMessage.content,
          );
          chatHistory = [lastMessage];
        }
      } else {
        chatHistory = [];
      }

      const key = await this.security.decrypt(agentConfig.model.key);

      const agent = this.graphConfig.buildAgentGraph({
        agentId: agentConfig.id,
        provider: agentConfig.model.provider,
        model: agentConfig.model.name,
        systemPrompt: agentConfig.systemPrompt,
        temperature: agentConfig.temperature,
        customRetriever: fileRetriever,
        key,
      });

      let accumulator = '';
      const maxDelay = 3000;

      const result = await agent.invoke(
        {
          messages,
          chat_history: chatHistory,
        },
        {
          signal,
        },
      );

      const lastMessage = result.messages[result.messages?.length - 1];

      let finalAnswer = '';
      if (typeof lastMessage.content === 'string') {
        finalAnswer = lastMessage.content;
      } else if (Array.isArray(lastMessage.content)) {
        finalAnswer = lastMessage.content
          .map((part) => {
            if (typeof part === 'string') {
              return part;
            } else if (part && typeof part === 'object' && 'text' in part) {
              return part.text;
            }
            return '';
          })
          .join('');
      }

      accumulator = finalAnswer;

      let yielded = false;

      for (const delimiter of Object.keys(this.typingConfig)) {
        if (accumulator.includes(delimiter)) {
          const parts = accumulator.split(delimiter);
          for (let i = 0; i < parts?.length - 1; i++) {
            const segment = parts[i].trim().replace(/\n/g, '');
            const delay = Math.min(
              segment?.length * this.typingConfig[delimiter],
              maxDelay,
            );

            await new Promise((r) => setTimeout(r, delay));

            if (segment.trim()) {
              this.logger.log(
                '---GENERATE ANSWER (STREAM): SAVE AI AGENT ANSWER TO DB---',
              );
              await this.chatMessageService.saveMessage(
                userId,
                agentId,
                ChatMessageRole.assistant,
                segment,
              );
              this.logger.debug(`---STREAM: ${segment}---`);
              yield segment;
              yielded = true;
            }
          }
          accumulator = parts[parts?.length - 1];
        }
      }

      if (!yielded && accumulator.trim()) {
        const chunks = accumulator.match(/.{1,80}/g) || []; // chunk every 80 chars
        for (const chunk of chunks) {
          const delay = Math.min(chunk?.length * 50, maxDelay);
          await new Promise((r) => setTimeout(r, delay));
          await this.chatMessageService.saveMessage(
            userId,
            agentId,
            ChatMessageRole.assistant,
            chunk,
          );
          this.logger.debug(`---STREAM (fallback): ${chunk}---`);
          yield chunk;
          yielded = true;
        }
      }

      // if (!yielded && accumulator.trim()) {
      //   this.logger.debug(`---STREAM FINAL: ${accumulator.trim()}---`);
      //   yield accumulator.trim();
      // }

      return finalAnswer;
    } catch (error) {
      this.logger.error(error);
      if (error instanceof NotFoundException) throw error;

      if (error instanceof BadRequestException) throw error;

      if (error instanceof InternalServerErrorException) throw error;
    }
  }
}
