export interface IAyrShareConfig {
  baseUrl: string;
  apiKey: string;
}

export interface ISocialMediaPost {
  postId: string;
  createdAt: Date;
  post?: string;
  postUrl?: string;
  commentsCount: number;
  platformTag: Platform;
}

export interface ISocialMediaComment {
  commentId: string;
  createdAt: Date;
  comment: string;
  replies?: ISocialMediaComment[];
  platformTag: Platform;
}

export enum Platform {
  FACEBOOK = 'facebook',
  INSTAGRAM = 'instagram',
  LINKEDIN = 'linkedin',
  TIKTOK = 'tiktok',
  YOUTUBE = 'youtube',
  BLUESKY = 'bluesky',
  PINTEREST = 'pinterest',
  TWITTER = 'twitter',
}
