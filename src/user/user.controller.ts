import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Res,
  UseFilters,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { Response } from 'express';
import { PrismaExceptionFilter } from 'src/exception/prisma-exception.filter';
import { UserService } from './user.service';
import { UserDto } from './user.dto';
import { ConfigService } from '@nestjs/config';

@Controller('user')
@UseFilters(PrismaExceptionFilter)
export class UserController {
  private secure: boolean;

  constructor(
    private readonly userService: UserService,
    private readonly configService: ConfigService,
  ) {
    this.secure =
      this.configService.get<string>('node_env') === 'production' ||
      this.configService.get<string>('node_env') === 'staging';
  }

  @Post()
  @UsePipes(new ValidationPipe())
  async create(@Body() createUserDto: UserDto, @Res() response: Response) {
    const userAuthDetails = await this.userService.createUser(createUserDto);

    return response.status(201).json({
      message: 'User account created successfully',
      accessToken: userAuthDetails.access_token,
      dateTime: new Date(),
    });
  }

  @Get()
  async getAll() {
    return this.userService.getAllUsers();
  }

  @Get(':id')
  async get(@Param('id') id: string) {
    return this.userService.getUser({ id });
  }

  @Put(':id')
  async update(@Param('id') id: string, @Body() updateUserDto: UserDto) {
    return this.userService.updateUser(id, updateUserDto);
  }

  @Delete(':id')
  async delete(@Param('id') id: string) {
    return this.userService.removeUser(id);
  }
}
