import {
  Body,
  Controller,
  Delete,
  Get,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ChatMessageService } from './chat-message.service';
import { Request } from 'express';
import { JwtAuthGuard } from 'src/auth/guards/jwt.guard';
import { CentralizeAIRequest } from 'src/types';
import { GetChatThreadDto } from './chat-message.dto';

@Controller('chat')
export class ChatMessageController {
  constructor(private readonly chatMessageService: ChatMessageService) {}

  @Post('/thread')
  @UseGuards(JwtAuthGuard)
  async getCustomerChatForSession(
    @Body() data: GetChatThreadDto,
    @Req() request: CentralizeAIRequest,
  ) {
    return this.chatMessageService.getChatHistory(
      request.user.userid,
      data.agentId,
    );
  }

  @Get()
  async getCustomerChats(
    @Query('skip') skip: number,
    @Query('take') take: number,
  ) {
    return this.chatMessageService.getCustomerChats({ skip, take });
  }

  @Delete('/thread')
  @UseGuards(JwtAuthGuard)
  async resetChatThread(
    @Body() data: GetChatThreadDto,
    @Req() request: CentralizeAIRequest,
  ) {
    return this.chatMessageService.resetChatThread(
      request.user.userid,
      data.agentId,
    );
  }
}
