import { Annotation, END, START, StateGraph } from '@langchain/langgraph';
import { ModelFactory, ModelProvider } from './model.factory';
import {
  AIMessage,
  BaseMessage,
  SystemMessage,
} from '@langchain/core/messages';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { RetrieveTool } from 'src/tools/retrieve-tool/retrieve-tool';
import { ToolNode } from '@langchain/langgraph/prebuilt';
import { v4 as uuidV4 } from 'uuid';
import { BaseRetriever } from '@langchain/core/retrievers';
import { DynamicTool } from '@langchain/core/tools';
import { PrismaService } from 'src/prisma.service';
import { InternalSystemPromptType } from '@prisma/client';

interface IGraphConfig {
  agentId: string;
  key: string;
  provider: ModelProvider;
  model: string;
  systemPrompt: string;
  temperature: number;
  customRetriever?: BaseRetriever;
}

@Injectable()
export class GraphConfig {
  private logger = new Logger(GraphConfig.name);
  private GraphState = Annotation.Root({
    messages: Annotation<BaseMessage[]>({
      reducer: (a: BaseMessage[], b: BaseMessage[]) => a.concat(b),
      default: () => [],
    }),
    chat_history: Annotation<BaseMessage[]>({
      reducer: (a, b) => a.concat(b),
      default: () => [],
    }),
    retryCount: Annotation<number>({
      reducer: (prev) => (prev ?? 0) + 1,
      default: () => 0,
    }),
  });
  private toolNode: ToolNode<typeof this.GraphState.State>;

  constructor(
    private readonly modelFactory: ModelFactory,
    private retrieveTool: RetrieveTool,
    private prisma: PrismaService,
  ) {
    this.toolNode = new ToolNode<typeof this.GraphState.State>([
      this.retrieveTool.getTool(),
    ]);
  }

  private shouldRetrieve(state: typeof this.GraphState.State): string {
    const { messages } = state;
    this.logger.log('---DECIDE TO RETRIEVE---');
    const lastMessage = messages[messages.length - 1];

    if (
      'tool_calls' in lastMessage &&
      Array.isArray(lastMessage.tool_calls) &&
      lastMessage.tool_calls.length
    ) {
      this.logger.log('---DECISION: RETRIEVE---');
      return 'retrieve';
    }

    this.logger.log('---DECISION: NO RETRIEVE---');
    return END;
  }

  private async summarizeHistory(
    state: typeof this.GraphState.State,
    config: IGraphConfig,
  ): Promise<Partial<typeof this.GraphState.State>> {
    try {
      const { chat_history } = state;

      const modelLLM = this.modelFactory.getLLM({
        provider: config.provider,
        model: config.model,
        temperature: config.temperature,
        streaming: false,
        key: config.key,
      });

      this.logger.log('---GET SUMMARIZE INTERNAL SYSTEM PROMPT---');
      const summarizePrompt = await this.prisma.internalSystemPrompts.findFirst(
        {
          where: {
            type: InternalSystemPromptType.summarize,
          },
        },
      );

      if (!summarizePrompt) {
        throw new NotFoundException(
          'Summarize internal system prompt not found',
        );
      }

      const prompt = ChatPromptTemplate.fromTemplate(summarizePrompt.prompt);

      const chain = prompt.pipe(modelLLM);

      const response = await chain.invoke({
        history: chat_history
          .map((msg) => `${msg.getType()}: ${msg.content}`)
          .join('\n'),
      });

      return { messages: [response] };
    } catch (error) {
      if (error instanceof NotFoundException) throw error;
    }
  }

  private async gradeDocuments(
    state: typeof this.GraphState.State,
    config: IGraphConfig,
  ): Promise<Partial<typeof this.GraphState.State>> {
    try {
      const { messages, chat_history } = state;

      const modelLLM = this.modelFactory.getLLM({
        provider: config.provider,
        model: config.model,
        temperature: config.temperature,
        streaming: false,
        tools: this.getRetrieverTools(config),
        key: config.key,
      });

      this.logger.log('---GET GRADE DOCUMENTS INTERNAL SYSTEM PROMPT---');
      const gradePrompt = await this.prisma.internalSystemPrompts.findFirst({
        where: {
          type: InternalSystemPromptType.grade_documents,
        },
      });

      if (!gradePrompt) {
        throw new NotFoundException('Grade internal system prompt not found');
      }

      const prompt = ChatPromptTemplate.fromTemplate(gradePrompt.prompt);

      const chain = prompt.pipe(modelLLM);
      const lastMessage = messages[messages.length - 1];
      const context =
        messages.find((msg) => msg.getType() === 'tool')?.content ?? '';
      const question = lastMessage.content;

      const response = await chain.invoke({
        context,
        question,
        history: chat_history
          .map((msg) => `${msg.getType()}: ${msg.content}`)
          .join('\n'),
      });

      const parsed = response.text?.trim().toLowerCase().includes('yes');

      const toolCallId = `tool-${uuidV4()}`;

      const toolCallMessage = new AIMessage({
        content: 'Relevance graded',
        tool_calls: [
          {
            name: 'give_relevance_score',
            args: {
              binaryScore: parsed ? 'yes' : 'no',
            },
            id: toolCallId,
          },
        ],
      });

      return { messages: [toolCallMessage], retryCount: 1 };
    } catch (error) {
      if (error instanceof NotFoundException) throw error;
    }
  }

  private checkRelevance(state: typeof this.GraphState.State): string {
    this.logger.log('---CHECK RELEVANCE---');

    const { messages, retryCount } = state;
    this.logger.log(`---RETRY COUNT: ${retryCount}---`);

    if (retryCount > 5) {
      this.logger.warn('Too many retries, forcing END');
      return 'yes';
    }

    const lastMessage = messages[messages.length - 1];

    if (!lastMessage || !('tool_calls' in lastMessage)) {
      this.logger.log('---DECISION: NO TOOL CALLS, ASSUMING NOT RELEVANT---');
      return 'no';
    }

    const toolCalls = (lastMessage as AIMessage).tool_calls;
    if (!toolCalls || !toolCalls.length) {
      this.logger.log(
        '---DECISION: EMPTY TOOL CALLS, ASSUMING NOT RELEVANT---',
      );
      return 'no';
    }

    if (toolCalls[0]?.args?.binaryScore !== 'yes') {
      this.logger.log('---DECISION: NO BINARY SCORE, ASSUMING NOT RELEVANT---');
      return 'no';
    }

    if (toolCalls[0].args.binaryScore === 'yes') {
      this.logger.log('---DECISION: DOCS RELEVANT---');
      return 'yes';
    }

    this.logger.log('---DECISION: DOCS NOT RELEVANT---');
    return 'no';
  }

  private async agent(
    state: typeof this.GraphState.State,
    config: IGraphConfig,
  ): Promise<Partial<typeof this.GraphState.State>> {
    try {
      const { messages, chat_history } = state;

      const modelLLM = this.modelFactory.getLLM({
        provider: config.provider,
        model: config.model,
        temperature: config.temperature,
        streaming: false,
        tools: this.getRetrieverTools(config),
        key: config.key,
      });

      const filteredMessages = this.filterUnRespondedToolCalls(messages);

      this.logger.log('---GET AGENT INTERNAL SYSTEM PROMPT---');
      const agentPrompt = await this.prisma.internalSystemPrompts.findFirst({
        where: {
          type: InternalSystemPromptType.agent,
        },
      });

      if (!agentPrompt) {
        throw new NotFoundException('Agent internal system prompt not found');
      }

      const systemPrompt = config.systemPrompt + '\n\n' + agentPrompt.prompt;

      const systemMessage = new SystemMessage(systemPrompt);

      const response = await modelLLM.invoke([
        systemMessage,
        ...chat_history,
        ...filteredMessages,
      ]);

      return { messages: [response], retryCount: 1 };
    } catch (error) {
      if (error instanceof NotFoundException) throw error;
    }
  }

  private filterUnRespondedToolCalls(messages: BaseMessage[]): BaseMessage[] {
    const result: BaseMessage[] = [];
    const respondedToolCallIds = new Set<string>();

    // First, collect all tool call IDs that have been responded to
    for (let i = 0; i < messages.length; i++) {
      const msg = messages[i];
      if (msg.getType() === 'tool') {
        // If this is a tool message, it should have a tool_call_id
        const toolCallId = (msg as any).tool_call_id;
        if (toolCallId) {
          respondedToolCallIds.add(toolCallId);
        }
      }
    }

    // Now filter messages, removing AI messages with un-responded tool calls
    for (let i = 0; i < messages.length; i++) {
      const msg = messages[i];

      if (
        msg.getType() === 'ai' &&
        'tool_calls' in msg &&
        Array.isArray(msg.tool_calls)
      ) {
        // Check if all tool calls in this message have been responded to
        const allToolCallsResponded = msg.tool_calls.every(
          (tc: any) => !tc.id || respondedToolCallIds.has(tc.id),
        );

        if (allToolCallsResponded) {
          result.push(msg);
        }
      } else {
        result.push(msg);
      }
    }

    return result;
  }

  private async rewrite(
    state: typeof this.GraphState.State,
    config: IGraphConfig,
  ): Promise<Partial<typeof this.GraphState.State>> {
    try {
      const { messages, chat_history } = state;
      const modelLLM = this.modelFactory.getLLM({
        provider: config.provider,
        model: config.model,
        temperature: config.temperature,
        streaming: false,
        tools: this.getRetrieverTools(config),
        key: config.key,
      });

      const question = messages[messages.length - 1].content;

      this.logger.log('---GET REWRITE INTERNAL SYSTEM PROMPT---');
      const rewritePrompt = await this.prisma.internalSystemPrompts.findFirst({
        where: {
          type: InternalSystemPromptType.rewrite,
        },
      });

      if (!rewritePrompt) {
        throw new NotFoundException('Rewrite internal system prompt not found');
      }

      const prompt = ChatPromptTemplate.fromTemplate(rewritePrompt.prompt);
      const chain = prompt.pipe(modelLLM);
      const response = await chain.invoke({
        question,
        history: chat_history
          .map((msg) => `${msg.getType()}: ${msg.content}`)
          .join('\n'),
      });

      return { messages: [response], retryCount: 1 };
    } catch (error) {
      if (error instanceof NotFoundException) throw error;
    }
  }

  private async generate(
    state: typeof this.GraphState.State,
    config: IGraphConfig,
  ): Promise<Partial<typeof this.GraphState.State>> {
    try {
      this.logger.log('---GENERATE---');
      const { messages, chat_history } = state;
      const modelLLM = this.modelFactory.getLLM({
        provider: config.provider,
        model: config.model,
        temperature: config.temperature,
        streaming: false,
        tools: this.getRetrieverTools(config),
        key: config.key,
      });
      const lastToolMessage = messages
        .slice()
        .reverse()
        .find((msg) => msg.getType() === 'tool');

      if (!lastToolMessage) {
        throw new Error('No tool message found in the conversation history');
      }

      const docs = lastToolMessage.content as string;

      this.logger.log('---GET GENERATE INTERNAL SYSTEM PROMPT---');
      const generatePrompt = await this.prisma.internalSystemPrompts.findFirst({
        where: {
          type: InternalSystemPromptType.generate,
        },
      });

      if (!generatePrompt) {
        throw new NotFoundException(
          'Generate internal system prompt not found',
        );
      }

      const systemPrompt = generatePrompt.prompt;

      const prompt = ChatPromptTemplate.fromTemplate(systemPrompt);
      const chain = prompt.pipe(modelLLM);
      const response = await chain.invoke({
        question: messages[0].content,
        docs,
        history: chat_history
          .map((msg) => `${msg.getType()}: ${msg.content}`)
          .join('\n'),
      });

      return { messages: [response] };
    } catch (error) {
      if (error instanceof NotFoundException) throw error;
    }
  }

  buildAgentGraph(config: IGraphConfig) {
    this.logger.log('---BUILD AGENT GRAPH---');
    this.logger.log(`---PROVIDER: ${config.provider}---`);
    this.logger.log(`---MODEL: ${config.model}---`);

    const boundAgent = (state: typeof this.GraphState.State) =>
      this.agent(state, config);

    const boundGradeDocuments = (state: typeof this.GraphState.State) =>
      this.gradeDocuments(state, config);

    const boundRewrite = (state: typeof this.GraphState.State) =>
      this.rewrite(state, config);

    const boundGenerate = (state: typeof this.GraphState.State) =>
      this.generate(state, config);

    const retrieverTool = this.getRetrieverTools(config);

    const builder = new StateGraph(this.GraphState)
      .addNode('agent', boundAgent)
      .addNode('retrieve', new ToolNode(retrieverTool))
      .addNode('gradeDocuments', boundGradeDocuments)
      .addNode('rewrite', boundRewrite)
      .addNode('generate', boundGenerate)
      .addEdge(START, 'agent')
      .addConditionalEdges('agent', this.shouldRetrieve.bind(this))
      .addEdge('retrieve', 'gradeDocuments')
      .addConditionalEdges('gradeDocuments', this.checkRelevance.bind(this), {
        yes: 'generate',
        no: 'rewrite',
      })
      .addEdge('generate', END)
      .addEdge('rewrite', 'agent');

    this.logger.log('---BUILD AGENT GRAPH COMPLETED---');

    return builder.compile();
  }

  buildSummarizationGraph(config: IGraphConfig) {
    const boundSummarizeHistory = (state: typeof this.GraphState.State) =>
      this.summarizeHistory(state, config);

    const builder = new StateGraph(this.GraphState)
      .addNode('summarize_history', boundSummarizeHistory)
      .addEdge(START, 'summarize_history')
      .addEdge('summarize_history', END);

    return builder.compile();
  }

  private getRetrieverTools(config: IGraphConfig) {
    const tools = [this.retrieveTool.getTool(config.agentId)];

    if (config.customRetriever) {
      tools.push(this.createSimpleRetrieverTool(config.customRetriever));
    }

    return tools;
  }

  private createSimpleRetrieverTool(retriever: BaseRetriever): DynamicTool {
    return new DynamicTool({
      name: 'uploaded_file_retriever',
      description:
        'Retrieve all the information from uploaded documents. Must return all the file contents.',
      func: async (input: string) => {
        const docs = await retriever.invoke(input);
        return docs.map((d) => d.pageContent).join('\n');
      },
    });
  }
}
