import { DynamicModule, Module } from '@nestjs/common';
import { AyrshareService } from './ayrshare.service';
import { IAyrShareConfig } from './ayrshare.config.interface';
import { ScheduleModule } from '@nestjs/schedule';
import { AyrsharePostService } from './ayrshare.post.service';
import { AyrShareCommentService } from './ayrshare.comment.service';

@Module({})
export class AyrshareModule {
  static forRoot(options: IAyrShareConfig): DynamicModule {
    return {
      module: AyrshareModule,
      imports: [ScheduleModule.forRoot()],
      providers: [
        {
          provide: 'AYRSHARE_MODULE_OPTIONS',
          useValue: options,
        },
        AyrshareService,
        AyrsharePostService,
        AyrShareCommentService,
      ],
      exports: [AyrshareService],
    };
  }
}
