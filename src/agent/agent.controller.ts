import {
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  Res,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { Response } from 'express';
import { AgentDto } from './agent.dto';
import { AgentManageService } from './agent.manage.service';
import { ConfigService } from '@nestjs/config';
import { RagAgentService } from 'src/rag-agent/rag-agent.service';
import { JwtAuthGuard } from 'src/auth/guards/jwt.guard';
import { CentralizeAIRequest } from 'src/types';
import { Roles } from 'src/auth/roles/roles.decorator';
import { UserRoles } from '@prisma/client';
import { RolesGuard } from 'src/auth/guards/role.guard';
import { FilesInterceptor } from '@nestjs/platform-express';
import { AbortService } from 'src/abort/abort.service';
import { InternalSystemPromptDto } from './internal-prompt.dto';

@Controller('agent')
export class AgentController {
  private readonly logger = new Logger(AgentController.name);

  constructor(
    private agentManageService: AgentManageService,
    private abortService: AbortService,
    private readonly configService: ConfigService,
    private ragAgentService: RagAgentService,
  ) {}

  // Agent management endpoints
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRoles.ADMIN, UserRoles.SUPER_ADMIN)
  @Post('/manage')
  async createAgent(@Body() data: AgentDto) {
    return this.agentManageService.createAgent(data);
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRoles.ADMIN, UserRoles.SUPER_ADMIN)
  @Get('/manage')
  async getAgents(@Query('skip') skip: number, @Query('take') take: number) {
    return await this.agentManageService.getAgents({ skip, take });
  }

  @UseGuards(JwtAuthGuard)
  @Get('/manage/:id')
  async getAgent(@Param() params: { id: string }) {
    return this.agentManageService.getAgent({ id: params.id });
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRoles.ADMIN, UserRoles.SUPER_ADMIN)
  @Put('/manage/:id')
  async updateAgent(@Param() params: { id: string }, @Body() data: AgentDto) {
    return this.agentManageService.updateAgent(params.id, data);
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRoles.ADMIN, UserRoles.SUPER_ADMIN)
  @Delete('/manage/:id')
  async deleteAgent(@Param() params: { id: string }) {
    return this.agentManageService.deleteAgent(params.id);
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRoles.SUPER_ADMIN, UserRoles.ADMIN)
  @Get('/internal-prompts')
  async getInternalPrompts() {
    this.logger.log('Get internal prompts');
    return this.agentManageService.getInternalSystemPrompts();
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRoles.SUPER_ADMIN, UserRoles.ADMIN)
  @Post('/internal-prompts')
  async upsertInternalPrompts(@Body() data: InternalSystemPromptDto) {
    return this.agentManageService.upsertInternalPrompts(data);
  }

  @UseGuards(JwtAuthGuard)
  @Post('ask')
  @UseInterceptors(
    FilesInterceptor('files', 3, {
      limits: { fileSize: 50 * 1024 * 1024 }, // 50MB
    }),
  )
  async agentReactExecuterGenerateAnswer(
    @Body('query') query: string,
    @Req() request: CentralizeAIRequest,
    @Res() response: Response,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    try {
      const agentId = request.headers?.agentid as string;
      const userId = request.user.userid;
      const sessionId = request.body.sessionId;

      if (!agentId) {
        return response
          .status(400)
          .json({ error: 'Agent ID is required in headers: Field - `agentid' });
      }

      if (!sessionId) {
        return response.status(400).json({
          error: 'Session ID is required in body: Field - `sessionId`',
        });
      }

      const reactAgentResponse =
        await this.ragAgentService.generateCompleteAnswer(
          query,
          userId,
          sessionId,
          agentId,
          files,
        );

      return response.status(200).json({
        userId,
        sessionId,
        question: query,
        answer: reactAgentResponse,
      });
    } catch (error) {
      this.logger.error(error.message, error.stack);
      return response.status(500).json({ error: 'Internal Server Error' });
    }
  }

  @UseGuards(JwtAuthGuard)
  @Post('stream')
  @UseInterceptors(
    FilesInterceptor('files', 3, {
      limits: { fileSize: 50 * 1024 * 1024 }, // 50MB
    }),
  )
  async stream(
    @Req() request: CentralizeAIRequest,
    @Res() response: Response,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    const agentId = request.headers?.agentid as string;
    const userId = request.user.userid;
    const sessionId = request.body.sessionId;
    const query = request.body.query;

    if (!agentId) {
      return response
        .status(400)
        .json({ error: 'Agent ID is required in headers: Field - `agentid`' });
    }

    try {
      const stream = this.ragAgentService.generateAnswer(
        query,
        userId,
        sessionId,
        agentId,
        files,
      );

      for await (const chunk of stream) {
        response.write(`data: ${chunk}\n\n`);
      }

      response.write('data: [END_STREAM]\n\n');
      response.end();
    } catch (error) {
      response.write(`data: [ERROR]: ${error.message || 'Unknown error'}\n\n`);
      response.end();
    }
  }

  @Get('/interrupt/:sessionId')
  stopGeneration(@Param() params: { sessionId: string }) {
    this.abortService.abort(params.sessionId);
    return { message: `Aborted session ${params.sessionId}` };
  }

  @Get('/test/call')
  async testCall() {
    return { message: 'Hello World' };
  }
}
