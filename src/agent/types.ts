import { BaseLanguageModelInput } from '@langchain/core/language_models/base';
import { AIMessageChunk, BaseMessage } from '@langchain/core/messages';
import { Runnable } from '@langchain/core/runnables';
import { AnnotationRoot, BinaryOperatorAggregate } from '@langchain/langgraph';
import { ChatOpenAI, ChatOpenAICallOptions } from '@langchain/openai';

export type ModelWithTools = Runnable<
  BaseLanguageModelInput,
  AIMessageChunk,
  ChatOpenAICallOptions
>;

export type OpenAIModel = ChatOpenAI<ChatOpenAICallOptions>;

export type GraphState = AnnotationRoot<{
  messages: BinaryOperatorAggregate<BaseMessage[], BaseMessage[]>;
  chat_history: BinaryOperatorAggregate<BaseMessage[], BaseMessage[]>;
}>;
