export default () => ({
  node_env: process.env.NODE_ENV,
  port: parseInt(process.env.PORT, 10) || 4000,
  auth: {
    jwt_secret: process.env.JWT_SECRET,
  },
  database: {
    url: process.env.DATABASE_URL,
    mongodb_atlas_url: process.env.MONGODB_ATLAS_URI,
    mongodb_atlas_db_name: process.env.MONGODB_ATLAS_DB_NAME,
    mongodb_atlas_collection_name: process.env.MONGODB_ATLAS_COLLECTION_NAME,
  },
  llm: {
    provider: process.env.LLM_PROVIDER || 'open_ai',
    openai_api_key: process.env.OPEN_AI_API_KEY,
    anthropic_api_key: process.env.ANTHROPIC_API_KEY,
    google_gemini_api_key: process.env.GEMINI_API_KEY,
    elevenlabs_api_key: process.env.ELEVENLABS_API_KEY,
  },
  fingerprint_js_api_key: process.env.FINGERPRINT_JS_API_KEY,
  ayrshare: {
    base_url: process.env.AYRSHARE_BASE_URL,
    api_key: process.env.AYRSHARE_API_KEY,
  },
  gmail: {
    refresh_token: process.env.GMAIL_REFRESH_TOKEN,
    client_id: process.env.GMAIL_CLIENT_ID,
    client_secret: process.env.GMAIL_CLIENT_SECRET,
    user: process.env.GMAIL_USER,
    notify_emails: JSON.parse(process.env.GMAIL_NOTIFY_EMAILS),
  },
  security: {
    encryption_key: process.env.ENCRYPTION_KEY,
  },
  ghl: {
    private_token: process.env.HIGHLEVEL_PRIVATE_TOKEN,
    sub_account_id: process.env.SUB_ACCOUNT_ID,
  },
});
