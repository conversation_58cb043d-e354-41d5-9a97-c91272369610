{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@app/openai": ["libs/openai/src"], "@app/openai/*": ["libs/openai/src/*"], "@app/fingerprint-js": ["libs/fingerprint-js/src"], "@app/fingerprint-js/*": ["libs/fingerprint-js/src/*"], "@app/ayrshare": ["libs/ayrshare/src"], "@app/ayrshare/*": ["libs/ayrshare/src/*"], "@app/ghl": ["libs/ghl/src"], "@app/ghl/*": ["libs/ghl/src/*"]}}}