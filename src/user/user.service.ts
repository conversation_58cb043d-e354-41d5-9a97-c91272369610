import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';
import { Prisma, User } from '@prisma/client';
import * as bcrypt from 'bcryptjs';
import { DefaultArgs } from '@prisma/client/runtime/library';
import { AuthService } from 'src/auth/auth.service';

@Injectable()
export class UserService {
  constructor(
    private prisma: PrismaService,
    @Inject(forwardRef(() => AuthService))
    private readonly authService: AuthService,
  ) {}

  async createUser(
    data: Prisma.UserCreateInput,
  ): Promise<{ user_id: string; access_token: string }> {
    const salt = await bcrypt.genSalt(10);
    const password = await bcrypt.hash(data.password, salt);
    data.password = password;

    const user = await this.prisma.user.create({ data });

    const authData = await this.authService.login({
      id: user.id,
      userName: user.userName,
      email: user.email,
      role: user.role,
    });

    return authData;
  }

  async getUser(
    where: Prisma.UserWhereUniqueInput,
    select?: Prisma.UserSelect<DefaultArgs>,
  ): Promise<User> {
    return await this.prisma.user.findUniqueOrThrow({ where, select });
  }

  async getAllUsers() {
    return await this.prisma.user.findMany({
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        userName: true,
        phoneNumber: true,
        role: true,
      },
    });
  }

  async updateUser(id: string, data: Prisma.UserUpdateInput): Promise<User> {
    return await this.prisma.user.update({ where: { id }, data });
  }

  async removeUser(
    id: string,
  ): Promise<{ id: string; message: string; dateTime: Date }> {
    await this.prisma.user.delete({ where: { id } });
    return {
      id,
      message: 'User deleted from the chatbot',
      dateTime: new Date(),
    };
  }
}
