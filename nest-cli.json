{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "src", "compilerOptions": {"deleteOutDir": true, "webpack": false}, "projects": {"fingerprint-js": {"type": "library", "root": "libs/fingerprint-js", "entryFile": "index", "sourceRoot": "libs/fingerprint-js/src", "compilerOptions": {"tsConfigPath": "libs/fingerprint-js/tsconfig.lib.json"}}, "ayrshare": {"type": "library", "root": "libs/ayrshare", "entryFile": "index", "sourceRoot": "libs/ayrshare/src", "compilerOptions": {"tsConfigPath": "libs/ayrshare/tsconfig.lib.json"}}, "ghl": {"type": "library", "root": "libs/ghl", "entryFile": "index", "sourceRoot": "libs/ghl/src", "compilerOptions": {"tsConfigPath": "libs/ghl/tsconfig.lib.json"}}}}