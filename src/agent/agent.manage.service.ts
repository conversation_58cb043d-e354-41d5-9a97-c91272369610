import {
  ConflictException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InternalSystemPrompts, Prisma } from '@prisma/client';
import { PrismaService } from 'src/prisma.service';
import { AgentDto } from './agent.dto';
import { InternalSystemPromptDto } from './internal-prompt.dto';

@Injectable()
export class AgentManageService {
  private logger = new Logger(AgentManageService.name);

  constructor(private readonly prisma: PrismaService) {}

  async createAgent(data: AgentDto) {
    try {
      return await this.prisma.agentConfig.create({
        data: {
          name: data.name,
          temperature: data.temperature,
          systemPrompt: data.systemPrompt,
          cacheEnabled: data.cacheEnabled,
          model: {
            connect: { id: data.modelId },
          },
          responseMode: data.responseMode,
        },
      });
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === 'P2002'
      ) {
        throw new ConflictException(
          `An agent with the same ${error.meta?.target?.[0]} already exists.`,
        );
      }

      // Re-throw other unexpected errors
      throw error;
    }
  }

  async getAgents({ skip = 0, take = 10 }: { skip?: number; take?: number }) {
    return await this.prisma.agentConfig.findMany({
      skip,
      take,
      include: {
        model: true,
      },
    });
  }

  async getAgent(where: Prisma.AgentConfigWhereInput) {
    try {
      return await this.prisma.agentConfig.findFirstOrThrow({
        where,
        include: {
          model: true,
        },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException('Agent not found');
      }
    }
  }

  async getAgentByName(name: string) {
    return await this.prisma.agentConfig.findUniqueOrThrow({
      where: {
        name,
      },
      include: {
        model: true,
      },
    });
  }

  async updateAgent(id: string, data: Prisma.AgentConfigUpdateInput) {
    try {
      const agentForUpdate = await this.getAgent({ id });

      if (!agentForUpdate) {
        throw new NotFoundException('Agent not found');
      }

      return await this.prisma.agentConfig.update({
        where: { id },
        data,
      });
    } catch (error) {
      if (error instanceof NotFoundException) throw error;

      throw new InternalServerErrorException(error);
    }
  }

  async deleteAgent(id: string) {
    return await this.prisma.agentConfig.delete({
      where: { id },
    });
  }

  async upsertInternalPrompts(data: InternalSystemPromptDto) {
    try {
      const id = data.id;
      let internalPrompt: InternalSystemPrompts | null = null;

      delete data.id;

      if (id) {
        this.logger.log('Find existing internal system prompt for Id: ', {
          data,
        });
        internalPrompt = await this.prisma.internalSystemPrompts.findUnique({
          where: {
            id,
          },
        });
      }

      if (internalPrompt) {
        this.logger.log('Update internal system prompt for Id: ', { data });
        return await this.prisma.internalSystemPrompts.update({
          where: { id },
          data,
        });
      } else {
        this.logger.log('Create internal system prompt', { data });
        return await this.prisma.internalSystemPrompts.create({
          data,
        });
      }
    } catch (error) {
      this.logger.error('Error with upsert internal system prompts', error);
      if (error instanceof NotFoundException) throw error;
    }
  }

  async getInternalSystemPrompts() {
    this.logger.log('Get internal system prompts');
    return await this.prisma.internalSystemPrompts.findMany();
  }
}
