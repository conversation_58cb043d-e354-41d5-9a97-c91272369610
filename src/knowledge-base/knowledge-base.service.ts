import { RecursiveCharacterTextSplitter } from '@langchain/textsplitters';
import { Document } from 'langchain/document';
import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { AgentManageService } from 'src/agent/agent.manage.service';
import { VectorStoreService } from 'src/vector-store/vector-store.service';
import { v4 as uuidV4 } from 'uuid';
import { PrismaService } from 'src/prisma.service';
import { ConfigService } from '@nestjs/config';
import { ObjectId } from 'mongodb';

@Injectable()
export class KnowledgeBaseService {
  private logger = new Logger(KnowledgeBaseService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly vectorStoreService: VectorStoreService,
    private readonly agentManageService: AgentManageService,
    private readonly prisma: PrismaService,
  ) {}

  async updateKnowledgeBase(
    name: string,
    description: string,
    agentId: string,
    id?: string,
  ) {
    if (!agentId) {
      throw new BadRequestException('Agent ID is required');
    }

    const agent = await this.agentManageService.getAgent({ id: agentId });

    if (!agent) {
      throw new NotFoundException('Agent not found');
    }

    const vectorStore = this.vectorStoreService.getVectorStore();
    const textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize: 1000, // TODO: Needs to be configurable externally
      chunkOverlap: 100,
    });

    const document = new Document({
      pageContent: description,
      metadata: {
        name,
        agentId,
        id: id || uuidV4(),
      },
    });

    const splitDocs = await textSplitter.splitDocuments([document]);

    if (id) {
      // TODO: Need to refactor this to prisma
      const dbName = this.configService.get<string>(
        'database.mongodb_atlas_db_name',
      );
      const knowledgeBase = await this.vectorStoreService
        .getMongoDbClient()
        .db(dbName)
        .collection('documents')
        .findOne({
          _id: new ObjectId(id),
        });

      if (knowledgeBase) {
        await this.vectorStoreService
          .getMongoDbClient()
          .db(dbName)
          .collection('documents')
          .deleteOne({
            _id: new ObjectId(id),
          });
      }
    }

    await vectorStore.addDocuments(splitDocs);

    return splitDocs;
  }

  async getKnowledgeBaseDocuments(agentId: string) {
    if (!agentId) {
      throw new BadRequestException('Agent ID is required');
    }

    const agent = await this.agentManageService.getAgent({ id: agentId });

    if (!agent) {
      throw new NotFoundException('Agent not found');
    }

    const docs = await this.prisma.documents.findMany({
      where: {
        agentId,
      },
      orderBy: {
        id: 'desc',
      },
      select: {
        id: true,
        name: true,
        content: true,
        agentId: true,
      },
    });

    return docs;
  }

  async getKnowledgeBase(agentId: string, query: string) {
    if (!agentId) {
      throw new BadRequestException('Agent ID is required');
    }

    const agent = await this.agentManageService.getAgent({ id: agentId });

    if (!agent) {
      throw new NotFoundException('Agent not found');
    }

    const vectorStore = this.vectorStoreService.getVectorStore();
    const retriever = await vectorStore.similaritySearch(query, 1000, {
      agentId,
    });

    const docs = retriever.map((doc) => {
      return {
        id: doc.metadata._id,
        name: doc.metadata.name,
        agentId: doc.metadata.agentId,
        content: doc.pageContent,
      };
    });

    return docs;
  }
}
